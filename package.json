{"name": "s<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "A comprehensive desktop application for managing monthly grocery lists with categories, budget tracking, and export features", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder", "postinstall": "electron-builder install-app-deps", "test": "mocha tests/**/*.test.js --timeout 10000", "test:watch": "mocha tests/**/*.test.js --watch --timeout 10000", "test:coverage": "nyc mocha tests/**/*.test.js --timeout 10000", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "css:build": "tailwindcss -i ./src/styles/input.css -o ./src/styles/main.css --watch"}, "keywords": ["groceries", "shopping-list", "electron", "desktop-app", "budget-tracking", "monthly-planning"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^37.4.0", "electron-builder": "^24.13.3", "mocha": "^10.2.0", "chai": "^4.3.8", "nyc": "^15.1.0", "eslint": "^8.50.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "dependencies": {"autoprefixer": "^10.4.20", "better-sqlite3": "^11.3.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "html-to-image": "^1.11.11", "postcss": "^8.4.47", "puppeteer": "^23.4.1", "sqlite3": "^5.1.7", "tailwindcss": "^3.4.13", "uuid": "^10.0.0"}, "build": {"appId": "com.obayedmamur.shodaipati", "productName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}