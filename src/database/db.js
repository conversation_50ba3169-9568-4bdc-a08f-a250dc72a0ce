const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

class DatabaseManager {
  constructor() {
    this.db = null;
    this.dbPath = null;
  }

  initialize() {
    try {
      // Get user data directory
      const userDataPath = app.getPath('userData');
      const dbDir = path.join(userDataPath, 'database');
      
      // Ensure database directory exists
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }
      
      this.dbPath = path.join(dbDir, 'shodaipati.db');
      
      // Initialize database connection
      this.db = new Database(this.dbPath);
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('foreign_keys = ON');
      
      // Run migrations
      this.runMigrations();
      
      console.log('Database initialized successfully at:', this.dbPath);
      return true;
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  runMigrations() {
    // Create tables if they don't exist
    this.createTables();
    this.insertDefaultCategories();
  }

  createTables() {
    // Categories table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        sort_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Items table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        quantity REAL DEFAULT 1,
        unit TEXT DEFAULT 'piece',
        brand TEXT,
        remarks TEXT,
        price REAL DEFAULT 0,
        category_id INTEGER NOT NULL,
        purchased BOOLEAN DEFAULT 0,
        date_added DATE DEFAULT CURRENT_DATE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
      )
    `);

    // Monthly lists table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS monthly_lists (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        month INTEGER NOT NULL,
        year INTEGER NOT NULL,
        name TEXT,
        budget REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(month, year)
      )
    `);

    // List items junction table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS list_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        list_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (list_id) REFERENCES monthly_lists (id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE CASCADE,
        UNIQUE(list_id, item_id)
      )
    `);

    // Templates table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Template items junction table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS template_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE CASCADE,
        UNIQUE(template_id, item_id)
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_items_category_id ON items(category_id);
      CREATE INDEX IF NOT EXISTS idx_items_purchased ON items(purchased);
      CREATE INDEX IF NOT EXISTS idx_items_date_added ON items(date_added);
      CREATE INDEX IF NOT EXISTS idx_list_items_list_id ON list_items(list_id);
      CREATE INDEX IF NOT EXISTS idx_template_items_template_id ON template_items(template_id);
    `);
  }

  insertDefaultCategories() {
    const defaultCategories = [
      { name: 'Vegetables', sort_order: 1 },
      { name: 'Fruits', sort_order: 2 },
      { name: 'Grains & Cereals', sort_order: 3 },
      { name: 'Dairy & Eggs', sort_order: 4 },
      { name: 'Meat & Seafood', sort_order: 5 },
      { name: 'Beverages', sort_order: 6 },
      { name: 'Snacks', sort_order: 7 },
      { name: 'Household Items', sort_order: 8 },
      { name: 'Personal Care', sort_order: 9 }
    ];

    const insertCategory = this.db.prepare(`
      INSERT OR IGNORE INTO categories (name, sort_order) VALUES (?, ?)
    `);

    for (const category of defaultCategories) {
      insertCategory.run(category.name, category.sort_order);
    }
  }

  getDatabase() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }

  // Backup functionality
  createBackup() {
    if (!this.db || !this.dbPath) {
      throw new Error('Database not initialized');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = this.dbPath.replace('.db', `_backup_${timestamp}.db`);
    
    try {
      fs.copyFileSync(this.dbPath, backupPath);
      return backupPath;
    } catch (error) {
      console.error('Backup creation failed:', error);
      throw error;
    }
  }
}

// Singleton instance
const dbManager = new DatabaseManager();

module.exports = dbManager;
