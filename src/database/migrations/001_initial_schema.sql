-- Initial database schema for Shodaipati grocery list application
-- This file serves as documentation for the database structure

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,
  sort_order INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Items table
CREATE TABLE IF NOT EXISTS items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  quantity REAL DEFAULT 1,
  unit TEXT DEFAULT 'piece',
  brand TEXT,
  remarks TEXT,
  price REAL DEFAULT 0,
  category_id INTEGER NOT NULL,
  purchased BOOLEAN DEFAULT 0,
  date_added DATE DEFAULT CURRENT_DATE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREI<PERSON>N KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
);

-- Monthly lists table
CREATE TABLE IF NOT EXISTS monthly_lists (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  month INTEGER NOT NULL,
  year INTEGER NOT NULL,
  name TEXT,
  budget REAL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(month, year)
);

-- List items junction table
CREATE TABLE IF NOT EXISTS list_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  list_id INTEGER NOT NULL,
  item_id INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (list_id) REFERENCES monthly_lists (id) ON DELETE CASCADE,
  FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE CASCADE,
  UNIQUE(list_id, item_id)
);

-- Templates table
CREATE TABLE IF NOT EXISTS templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Template items junction table
CREATE TABLE IF NOT EXISTS template_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  template_id INTEGER NOT NULL,
  item_id INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE CASCADE,
  FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE CASCADE,
  UNIQUE(template_id, item_id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_items_category_id ON items(category_id);
CREATE INDEX IF NOT EXISTS idx_items_purchased ON items(purchased);
CREATE INDEX IF NOT EXISTS idx_items_date_added ON items(date_added);
CREATE INDEX IF NOT EXISTS idx_list_items_list_id ON list_items(list_id);
CREATE INDEX IF NOT EXISTS idx_template_items_template_id ON template_items(template_id);

-- Insert default categories
INSERT OR IGNORE INTO categories (name, sort_order) VALUES 
  ('Vegetables', 1),
  ('Fruits', 2),
  ('Grains & Cereals', 3),
  ('Dairy & Eggs', 4),
  ('Meat & Seafood', 5),
  ('Beverages', 6),
  ('Snacks', 7),
  ('Household Items', 8),
  ('Personal Care', 9);
