const dbManager = require('../db');

class Category {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.sort_order = data.sort_order || 0;
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  static getAll() {
    const db = dbManager.getDatabase();
    const stmt = db.prepare(`
      SELECT * FROM categories 
      ORDER BY sort_order ASC, name ASC
    `);
    
    const rows = stmt.all();
    return rows.map(row => new Category(row));
  }

  static getById(id) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare('SELECT * FROM categories WHERE id = ?');
    const row = stmt.get(id);
    
    return row ? new Category(row) : null;
  }

  static getByName(name) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare('SELECT * FROM categories WHERE name = ?');
    const row = stmt.get(name);
    
    return row ? new Category(row) : null;
  }

  static create(data) {
    const db = dbManager.getDatabase();
    
    // Validate required fields
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Category name is required');
    }

    if (data.name.length > 30) {
      throw new Error('Category name must be 30 characters or less');
    }

    // Check for duplicates
    const existing = Category.getByName(data.name.trim());
    if (existing) {
      throw new Error('Category with this name already exists');
    }

    // Get next sort order if not provided
    let sortOrder = data.sort_order;
    if (sortOrder === undefined || sortOrder === null) {
      const maxOrderStmt = db.prepare('SELECT MAX(sort_order) as max_order FROM categories');
      const result = maxOrderStmt.get();
      sortOrder = (result.max_order || 0) + 1;
    }

    const stmt = db.prepare(`
      INSERT INTO categories (name, sort_order)
      VALUES (?, ?)
    `);

    const result = stmt.run(data.name.trim(), sortOrder);
    
    return Category.getById(result.lastInsertRowid);
  }

  static update(id, data) {
    const db = dbManager.getDatabase();
    
    const category = Category.getById(id);
    if (!category) {
      throw new Error('Category not found');
    }

    // Validate name if provided
    if (data.name !== undefined) {
      if (!data.name || data.name.trim().length === 0) {
        throw new Error('Category name is required');
      }

      if (data.name.length > 30) {
        throw new Error('Category name must be 30 characters or less');
      }

      // Check for duplicates (excluding current category)
      const existing = Category.getByName(data.name.trim());
      if (existing && existing.id !== id) {
        throw new Error('Category with this name already exists');
      }
    }

    const updates = [];
    const values = [];

    if (data.name !== undefined) {
      updates.push('name = ?');
      values.push(data.name.trim());
    }

    if (data.sort_order !== undefined) {
      updates.push('sort_order = ?');
      values.push(data.sort_order);
    }

    if (updates.length === 0) {
      return category;
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const stmt = db.prepare(`
      UPDATE categories 
      SET ${updates.join(', ')}
      WHERE id = ?
    `);

    stmt.run(...values);
    
    return Category.getById(id);
  }

  static delete(id) {
    const db = dbManager.getDatabase();
    
    const category = Category.getById(id);
    if (!category) {
      throw new Error('Category not found');
    }

    // Check if category has items
    const itemCountStmt = db.prepare('SELECT COUNT(*) as count FROM items WHERE category_id = ?');
    const itemCount = itemCountStmt.get(id);

    if (itemCount.count > 0) {
      throw new Error('Cannot delete category that contains items. Please reassign or delete items first.');
    }

    const stmt = db.prepare('DELETE FROM categories WHERE id = ?');
    const result = stmt.run(id);

    return result.changes > 0;
  }

  static reorder(categoryIds) {
    const db = dbManager.getDatabase();
    
    const updateStmt = db.prepare('UPDATE categories SET sort_order = ? WHERE id = ?');
    
    const transaction = db.transaction(() => {
      categoryIds.forEach((id, index) => {
        updateStmt.run(index + 1, id);
      });
    });

    transaction();
    
    return Category.getAll();
  }

  static reassignItems(fromCategoryId, toCategoryId) {
    const db = dbManager.getDatabase();
    
    // Validate categories exist
    const fromCategory = Category.getById(fromCategoryId);
    const toCategory = Category.getById(toCategoryId);
    
    if (!fromCategory) {
      throw new Error('Source category not found');
    }
    
    if (!toCategory) {
      throw new Error('Destination category not found');
    }

    const stmt = db.prepare(`
      UPDATE items 
      SET category_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE category_id = ?
    `);

    const result = stmt.run(toCategoryId, fromCategoryId);
    
    return result.changes;
  }

  static getItemCount(categoryId) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare('SELECT COUNT(*) as count FROM items WHERE category_id = ?');
    const result = stmt.get(categoryId);
    
    return result.count;
  }

  save() {
    if (this.id) {
      return Category.update(this.id, {
        name: this.name,
        sort_order: this.sort_order
      });
    } else {
      return Category.create({
        name: this.name,
        sort_order: this.sort_order
      });
    }
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      sort_order: this.sort_order,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = Category;
