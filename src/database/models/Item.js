const dbManager = require('../db');
const { format } = require('date-fns');

class Item {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.quantity = data.quantity || 1;
    this.unit = data.unit || 'piece';
    this.brand = data.brand || '';
    this.remarks = data.remarks || '';
    this.price = data.price || 0;
    this.category_id = data.category_id || null;
    this.purchased = Boolean(data.purchased);
    this.date_added = data.date_added || format(new Date(), 'yyyy-MM-dd');
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  static getAll(filters = {}) {
    const db = dbManager.getDatabase();
    let query = `
      SELECT i.*, c.name as category_name 
      FROM items i
      LEFT JOIN categories c ON i.category_id = c.id
    `;
    
    const conditions = [];
    const params = [];

    if (filters.category_id) {
      conditions.push('i.category_id = ?');
      params.push(filters.category_id);
    }

    if (filters.purchased !== undefined) {
      conditions.push('i.purchased = ?');
      params.push(filters.purchased ? 1 : 0);
    }

    if (filters.search) {
      conditions.push('(i.name LIKE ? OR i.brand LIKE ? OR c.name LIKE ?)');
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    if (filters.date_from) {
      conditions.push('i.date_added >= ?');
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      conditions.push('i.date_added <= ?');
      params.push(filters.date_to);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY c.sort_order ASC, i.name ASC';

    const stmt = db.prepare(query);
    const rows = stmt.all(...params);
    
    return rows.map(row => {
      const item = new Item(row);
      item.category_name = row.category_name;
      return item;
    });
  }

  static getById(id) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare(`
      SELECT i.*, c.name as category_name 
      FROM items i
      LEFT JOIN categories c ON i.category_id = c.id
      WHERE i.id = ?
    `);
    const row = stmt.get(id);
    
    if (row) {
      const item = new Item(row);
      item.category_name = row.category_name;
      return item;
    }
    
    return null;
  }

  static getByCategory(categoryId) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare(`
      SELECT i.*, c.name as category_name 
      FROM items i
      LEFT JOIN categories c ON i.category_id = c.id
      WHERE i.category_id = ?
      ORDER BY i.name ASC
    `);
    const rows = stmt.all(categoryId);
    
    return rows.map(row => {
      const item = new Item(row);
      item.category_name = row.category_name;
      return item;
    });
  }

  static create(data) {
    const db = dbManager.getDatabase();
    
    // Validate required fields
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Item name is required');
    }

    if (data.name.length > 100) {
      throw new Error('Item name must be 100 characters or less');
    }

    if (!data.category_id) {
      throw new Error('Category is required');
    }

    if (data.brand && data.brand.length > 50) {
      throw new Error('Brand name must be 50 characters or less');
    }

    if (data.remarks && data.remarks.length > 200) {
      throw new Error('Remarks must be 200 characters or less');
    }

    if (data.quantity && (data.quantity <= 0 || isNaN(data.quantity))) {
      throw new Error('Quantity must be a positive number');
    }

    if (data.price && (data.price < 0 || isNaN(data.price))) {
      throw new Error('Price must be a non-negative number');
    }

    const validUnits = ['Kg', 'g', 'L', 'mL', 'piece', 'dozen', 'pack'];
    if (data.unit && !validUnits.includes(data.unit)) {
      throw new Error('Invalid unit. Must be one of: ' + validUnits.join(', '));
    }

    const stmt = db.prepare(`
      INSERT INTO items (name, quantity, unit, brand, remarks, price, category_id, purchased, date_added)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      data.name.trim(),
      data.quantity || 1,
      data.unit || 'piece',
      data.brand ? data.brand.trim() : '',
      data.remarks ? data.remarks.trim() : '',
      data.price || 0,
      data.category_id,
      data.purchased ? 1 : 0,
      data.date_added || format(new Date(), 'yyyy-MM-dd')
    );
    
    return Item.getById(result.lastInsertRowid);
  }

  static update(id, data) {
    const db = dbManager.getDatabase();
    
    const item = Item.getById(id);
    if (!item) {
      throw new Error('Item not found');
    }

    // Validate fields if provided
    if (data.name !== undefined) {
      if (!data.name || data.name.trim().length === 0) {
        throw new Error('Item name is required');
      }
      if (data.name.length > 100) {
        throw new Error('Item name must be 100 characters or less');
      }
    }

    if (data.brand !== undefined && data.brand && data.brand.length > 50) {
      throw new Error('Brand name must be 50 characters or less');
    }

    if (data.remarks !== undefined && data.remarks && data.remarks.length > 200) {
      throw new Error('Remarks must be 200 characters or less');
    }

    if (data.quantity !== undefined && (data.quantity <= 0 || isNaN(data.quantity))) {
      throw new Error('Quantity must be a positive number');
    }

    if (data.price !== undefined && (data.price < 0 || isNaN(data.price))) {
      throw new Error('Price must be a non-negative number');
    }

    const validUnits = ['Kg', 'g', 'L', 'mL', 'piece', 'dozen', 'pack'];
    if (data.unit !== undefined && !validUnits.includes(data.unit)) {
      throw new Error('Invalid unit. Must be one of: ' + validUnits.join(', '));
    }

    const updates = [];
    const values = [];

    const fields = ['name', 'quantity', 'unit', 'brand', 'remarks', 'price', 'category_id', 'purchased', 'date_added'];
    
    fields.forEach(field => {
      if (data[field] !== undefined) {
        updates.push(`${field} = ?`);
        if (field === 'purchased') {
          values.push(data[field] ? 1 : 0);
        } else if (field === 'name' || field === 'brand' || field === 'remarks') {
          values.push(data[field] ? data[field].trim() : '');
        } else {
          values.push(data[field]);
        }
      }
    });

    if (updates.length === 0) {
      return item;
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const stmt = db.prepare(`
      UPDATE items 
      SET ${updates.join(', ')}
      WHERE id = ?
    `);

    stmt.run(...values);
    
    return Item.getById(id);
  }

  static delete(id) {
    const db = dbManager.getDatabase();
    
    const item = Item.getById(id);
    if (!item) {
      throw new Error('Item not found');
    }

    const stmt = db.prepare('DELETE FROM items WHERE id = ?');
    const result = stmt.run(id);

    return result.changes > 0;
  }

  static deleteMultiple(ids) {
    const db = dbManager.getDatabase();
    
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid item IDs provided');
    }

    const placeholders = ids.map(() => '?').join(',');
    const stmt = db.prepare(`DELETE FROM items WHERE id IN (${placeholders})`);
    const result = stmt.run(...ids);

    return result.changes;
  }

  static markPurchased(id, purchased = true) {
    return Item.update(id, { purchased });
  }

  static markMultiplePurchased(ids, purchased = true) {
    const db = dbManager.getDatabase();
    
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid item IDs provided');
    }

    const placeholders = ids.map(() => '?').join(',');
    const stmt = db.prepare(`
      UPDATE items 
      SET purchased = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id IN (${placeholders})
    `);
    
    const result = stmt.run(purchased ? 1 : 0, ...ids);
    return result.changes;
  }

  static getStatistics() {
    const db = dbManager.getDatabase();
    
    const totalStmt = db.prepare('SELECT COUNT(*) as total FROM items');
    const purchasedStmt = db.prepare('SELECT COUNT(*) as purchased FROM items WHERE purchased = 1');
    const totalValueStmt = db.prepare('SELECT SUM(price * quantity) as total_value FROM items');
    const purchasedValueStmt = db.prepare('SELECT SUM(price * quantity) as purchased_value FROM items WHERE purchased = 1');
    
    const total = totalStmt.get().total;
    const purchased = purchasedStmt.get().purchased;
    const totalValue = totalValueStmt.get().total_value || 0;
    const purchasedValue = purchasedValueStmt.get().purchased_value || 0;
    
    return {
      total,
      purchased,
      remaining: total - purchased,
      totalValue,
      purchasedValue,
      remainingValue: totalValue - purchasedValue
    };
  }

  save() {
    if (this.id) {
      return Item.update(this.id, {
        name: this.name,
        quantity: this.quantity,
        unit: this.unit,
        brand: this.brand,
        remarks: this.remarks,
        price: this.price,
        category_id: this.category_id,
        purchased: this.purchased,
        date_added: this.date_added
      });
    } else {
      return Item.create({
        name: this.name,
        quantity: this.quantity,
        unit: this.unit,
        brand: this.brand,
        remarks: this.remarks,
        price: this.price,
        category_id: this.category_id,
        purchased: this.purchased,
        date_added: this.date_added
      });
    }
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      quantity: this.quantity,
      unit: this.unit,
      brand: this.brand,
      remarks: this.remarks,
      price: this.price,
      category_id: this.category_id,
      purchased: this.purchased,
      date_added: this.date_added,
      created_at: this.created_at,
      updated_at: this.updated_at,
      category_name: this.category_name
    };
  }
}

module.exports = Item;
