const dbManager = require('../db');
const { format, startOfMonth, endOfMonth } = require('date-fns');

class MonthlyList {
  constructor(data = {}) {
    this.id = data.id || null;
    this.month = data.month || new Date().getMonth() + 1;
    this.year = data.year || new Date().getFullYear();
    this.name = data.name || '';
    this.budget = data.budget || 0;
    this.created_at = data.created_at || null;
  }

  static getAll() {
    const db = dbManager.getDatabase();
    const stmt = db.prepare(`
      SELECT * FROM monthly_lists 
      ORDER BY year DESC, month DESC
    `);
    
    const rows = stmt.all();
    return rows.map(row => new MonthlyList(row));
  }

  static getById(id) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare('SELECT * FROM monthly_lists WHERE id = ?');
    const row = stmt.get(id);
    
    return row ? new MonthlyList(row) : null;
  }

  static getByMonthYear(month, year) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare('SELECT * FROM monthly_lists WHERE month = ? AND year = ?');
    const row = stmt.get(month, year);
    
    return row ? new MonthlyList(row) : null;
  }

  static getCurrentMonth() {
    const now = new Date();
    return MonthlyList.getByMonthYear(now.getMonth() + 1, now.getFullYear());
  }

  static create(data) {
    const db = dbManager.getDatabase();
    
    // Validate required fields
    if (!data.month || !data.year) {
      throw new Error('Month and year are required');
    }

    if (data.month < 1 || data.month > 12) {
      throw new Error('Month must be between 1 and 12');
    }

    if (data.year < 2000 || data.year > 2100) {
      throw new Error('Year must be between 2000 and 2100');
    }

    // Check for duplicates
    const existing = MonthlyList.getByMonthYear(data.month, data.year);
    if (existing) {
      throw new Error('Monthly list for this month already exists');
    }

    const stmt = db.prepare(`
      INSERT INTO monthly_lists (month, year, name, budget)
      VALUES (?, ?, ?, ?)
    `);

    const result = stmt.run(
      data.month,
      data.year,
      data.name || '',
      data.budget || 0
    );
    
    return MonthlyList.getById(result.lastInsertRowid);
  }

  static update(id, data) {
    const db = dbManager.getDatabase();
    
    const monthlyList = MonthlyList.getById(id);
    if (!monthlyList) {
      throw new Error('Monthly list not found');
    }

    // Validate fields if provided
    if (data.month !== undefined && (data.month < 1 || data.month > 12)) {
      throw new Error('Month must be between 1 and 12');
    }

    if (data.year !== undefined && (data.year < 2000 || data.year > 2100)) {
      throw new Error('Year must be between 2000 and 2100');
    }

    // Check for duplicates if month/year is being changed
    if ((data.month !== undefined && data.month !== monthlyList.month) ||
        (data.year !== undefined && data.year !== monthlyList.year)) {
      const newMonth = data.month !== undefined ? data.month : monthlyList.month;
      const newYear = data.year !== undefined ? data.year : monthlyList.year;
      
      const existing = MonthlyList.getByMonthYear(newMonth, newYear);
      if (existing && existing.id !== id) {
        throw new Error('Monthly list for this month already exists');
      }
    }

    const updates = [];
    const values = [];

    const fields = ['month', 'year', 'name', 'budget'];
    
    fields.forEach(field => {
      if (data[field] !== undefined) {
        updates.push(`${field} = ?`);
        values.push(data[field]);
      }
    });

    if (updates.length === 0) {
      return monthlyList;
    }

    values.push(id);

    const stmt = db.prepare(`
      UPDATE monthly_lists 
      SET ${updates.join(', ')}
      WHERE id = ?
    `);

    stmt.run(...values);
    
    return MonthlyList.getById(id);
  }

  static delete(id) {
    const db = dbManager.getDatabase();
    
    const monthlyList = MonthlyList.getById(id);
    if (!monthlyList) {
      throw new Error('Monthly list not found');
    }

    // Delete associated list items first
    const deleteListItemsStmt = db.prepare('DELETE FROM list_items WHERE list_id = ?');
    deleteListItemsStmt.run(id);

    // Delete the monthly list
    const stmt = db.prepare('DELETE FROM monthly_lists WHERE id = ?');
    const result = stmt.run(id);

    return result.changes > 0;
  }

  static getItemsForMonth(month, year) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare(`
      SELECT i.*, c.name as category_name, ml.id as list_id
      FROM items i
      LEFT JOIN categories c ON i.category_id = c.id
      LEFT JOIN list_items li ON i.id = li.item_id
      LEFT JOIN monthly_lists ml ON li.list_id = ml.id
      WHERE ml.month = ? AND ml.year = ?
      ORDER BY c.sort_order ASC, i.name ASC
    `);
    
    return stmt.all(month, year);
  }

  static addItemToMonth(itemId, month, year) {
    const db = dbManager.getDatabase();
    
    // Get or create monthly list
    let monthlyList = MonthlyList.getByMonthYear(month, year);
    if (!monthlyList) {
      monthlyList = MonthlyList.create({ month, year });
    }

    // Check if item is already in the list
    const existingStmt = db.prepare('SELECT id FROM list_items WHERE list_id = ? AND item_id = ?');
    const existing = existingStmt.get(monthlyList.id, itemId);
    
    if (existing) {
      throw new Error('Item is already in this monthly list');
    }

    // Add item to list
    const stmt = db.prepare('INSERT INTO list_items (list_id, item_id) VALUES (?, ?)');
    stmt.run(monthlyList.id, itemId);
    
    return true;
  }

  static removeItemFromMonth(itemId, month, year) {
    const db = dbManager.getDatabase();
    
    const monthlyList = MonthlyList.getByMonthYear(month, year);
    if (!monthlyList) {
      throw new Error('Monthly list not found');
    }

    const stmt = db.prepare('DELETE FROM list_items WHERE list_id = ? AND item_id = ?');
    const result = stmt.run(monthlyList.id, itemId);
    
    return result.changes > 0;
  }

  static getStatisticsForMonth(month, year) {
    const db = dbManager.getDatabase();
    
    const monthlyList = MonthlyList.getByMonthYear(month, year);
    if (!monthlyList) {
      return {
        totalItems: 0,
        purchasedItems: 0,
        totalValue: 0,
        purchasedValue: 0,
        budget: 0,
        remainingBudget: 0
      };
    }

    const statsStmt = db.prepare(`
      SELECT 
        COUNT(*) as total_items,
        SUM(CASE WHEN i.purchased = 1 THEN 1 ELSE 0 END) as purchased_items,
        SUM(i.price * i.quantity) as total_value,
        SUM(CASE WHEN i.purchased = 1 THEN i.price * i.quantity ELSE 0 END) as purchased_value
      FROM items i
      JOIN list_items li ON i.id = li.item_id
      WHERE li.list_id = ?
    `);
    
    const stats = statsStmt.get(monthlyList.id);
    
    return {
      totalItems: stats.total_items || 0,
      purchasedItems: stats.purchased_items || 0,
      totalValue: stats.total_value || 0,
      purchasedValue: stats.purchased_value || 0,
      budget: monthlyList.budget,
      remainingBudget: monthlyList.budget - (stats.purchased_value || 0)
    };
  }

  static compareMonths(month1, year1, month2, year2) {
    const db = dbManager.getDatabase();
    
    const getMonthItemsStmt = db.prepare(`
      SELECT i.name, i.quantity, i.unit, i.price, c.name as category_name
      FROM items i
      LEFT JOIN categories c ON i.category_id = c.id
      LEFT JOIN list_items li ON i.id = li.item_id
      LEFT JOIN monthly_lists ml ON li.list_id = ml.id
      WHERE ml.month = ? AND ml.year = ?
      ORDER BY i.name ASC
    `);
    
    const month1Items = getMonthItemsStmt.all(month1, year1);
    const month2Items = getMonthItemsStmt.all(month2, year2);
    
    const month1Names = new Set(month1Items.map(item => item.name.toLowerCase()));
    const month2Names = new Set(month2Items.map(item => item.name.toLowerCase()));
    
    const common = month1Items.filter(item => month2Names.has(item.name.toLowerCase()));
    const onlyInMonth1 = month1Items.filter(item => !month2Names.has(item.name.toLowerCase()));
    const onlyInMonth2 = month2Items.filter(item => !month1Names.has(item.name.toLowerCase()));
    
    return {
      month1: { month: month1, year: year1, items: month1Items },
      month2: { month: month2, year: year2, items: month2Items },
      common,
      onlyInMonth1,
      onlyInMonth2,
      similarity: common.length / Math.max(month1Items.length, month2Items.length, 1)
    };
  }

  static getRecentMonths(limit = 12) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare(`
      SELECT * FROM monthly_lists 
      ORDER BY year DESC, month DESC
      LIMIT ?
    `);
    
    const rows = stmt.all(limit);
    return rows.map(row => new MonthlyList(row));
  }

  getItems() {
    return MonthlyList.getItemsForMonth(this.month, this.year);
  }

  getStatistics() {
    return MonthlyList.getStatisticsForMonth(this.month, this.year);
  }

  getDisplayName() {
    const date = new Date(this.year, this.month - 1);
    const monthName = date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    return this.name || monthName;
  }

  save() {
    if (this.id) {
      return MonthlyList.update(this.id, {
        month: this.month,
        year: this.year,
        name: this.name,
        budget: this.budget
      });
    } else {
      return MonthlyList.create({
        month: this.month,
        year: this.year,
        name: this.name,
        budget: this.budget
      });
    }
  }

  toJSON() {
    return {
      id: this.id,
      month: this.month,
      year: this.year,
      name: this.name,
      budget: this.budget,
      created_at: this.created_at,
      displayName: this.getDisplayName()
    };
  }
}

module.exports = MonthlyList;
