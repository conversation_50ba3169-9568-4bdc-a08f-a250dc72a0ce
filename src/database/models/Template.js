const dbManager = require('../db');

class Template {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.description = data.description || '';
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  static getAll() {
    const db = dbManager.getDatabase();
    const stmt = db.prepare(`
      SELECT * FROM templates 
      ORDER BY name ASC
    `);
    
    const rows = stmt.all();
    return rows.map(row => new Template(row));
  }

  static getById(id) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare('SELECT * FROM templates WHERE id = ?');
    const row = stmt.get(id);
    
    return row ? new Template(row) : null;
  }

  static getByName(name) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare('SELECT * FROM templates WHERE name = ?');
    const row = stmt.get(name);
    
    return row ? new Template(row) : null;
  }

  static create(data) {
    const db = dbManager.getDatabase();
    
    // Validate required fields
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Template name is required');
    }

    if (data.name.length > 100) {
      throw new Error('Template name must be 100 characters or less');
    }

    // Check for duplicates
    const existing = Template.getByName(data.name.trim());
    if (existing) {
      throw new Error('Template with this name already exists');
    }

    const stmt = db.prepare(`
      INSERT INTO templates (name, description)
      VALUES (?, ?)
    `);

    const result = stmt.run(
      data.name.trim(),
      data.description ? data.description.trim() : ''
    );
    
    return Template.getById(result.lastInsertRowid);
  }

  static update(id, data) {
    const db = dbManager.getDatabase();
    
    const template = Template.getById(id);
    if (!template) {
      throw new Error('Template not found');
    }

    // Validate name if provided
    if (data.name !== undefined) {
      if (!data.name || data.name.trim().length === 0) {
        throw new Error('Template name is required');
      }

      if (data.name.length > 100) {
        throw new Error('Template name must be 100 characters or less');
      }

      // Check for duplicates (excluding current template)
      const existing = Template.getByName(data.name.trim());
      if (existing && existing.id !== id) {
        throw new Error('Template with this name already exists');
      }
    }

    const updates = [];
    const values = [];

    if (data.name !== undefined) {
      updates.push('name = ?');
      values.push(data.name.trim());
    }

    if (data.description !== undefined) {
      updates.push('description = ?');
      values.push(data.description ? data.description.trim() : '');
    }

    if (updates.length === 0) {
      return template;
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const stmt = db.prepare(`
      UPDATE templates 
      SET ${updates.join(', ')}
      WHERE id = ?
    `);

    stmt.run(...values);
    
    return Template.getById(id);
  }

  static delete(id) {
    const db = dbManager.getDatabase();
    
    const template = Template.getById(id);
    if (!template) {
      throw new Error('Template not found');
    }

    // Delete associated template items first (handled by foreign key cascade)
    const stmt = db.prepare('DELETE FROM templates WHERE id = ?');
    const result = stmt.run(id);

    return result.changes > 0;
  }

  static getItemsForTemplate(templateId) {
    const db = dbManager.getDatabase();
    const stmt = db.prepare(`
      SELECT i.*, c.name as category_name
      FROM items i
      LEFT JOIN categories c ON i.category_id = c.id
      JOIN template_items ti ON i.id = ti.item_id
      WHERE ti.template_id = ?
      ORDER BY c.sort_order ASC, i.name ASC
    `);
    
    return stmt.all(templateId);
  }

  static addItemToTemplate(templateId, itemId) {
    const db = dbManager.getDatabase();
    
    // Check if template exists
    const template = Template.getById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Check if item is already in the template
    const existingStmt = db.prepare('SELECT id FROM template_items WHERE template_id = ? AND item_id = ?');
    const existing = existingStmt.get(templateId, itemId);
    
    if (existing) {
      throw new Error('Item is already in this template');
    }

    // Add item to template
    const stmt = db.prepare('INSERT INTO template_items (template_id, item_id) VALUES (?, ?)');
    stmt.run(templateId, itemId);
    
    return true;
  }

  static removeItemFromTemplate(templateId, itemId) {
    const db = dbManager.getDatabase();
    
    const stmt = db.prepare('DELETE FROM template_items WHERE template_id = ? AND item_id = ?');
    const result = stmt.run(templateId, itemId);
    
    return result.changes > 0;
  }

  static createFromCurrentItems(name, description, itemIds) {
    const db = dbManager.getDatabase();
    
    if (!itemIds || itemIds.length === 0) {
      throw new Error('No items selected for template');
    }

    // Create template
    const template = Template.create({ name, description });
    
    // Add items to template
    const addItemStmt = db.prepare('INSERT INTO template_items (template_id, item_id) VALUES (?, ?)');
    
    const transaction = db.transaction(() => {
      itemIds.forEach(itemId => {
        addItemStmt.run(template.id, itemId);
      });
    });

    transaction();
    
    return template;
  }

  static createFromMonthlyList(name, description, month, year) {
    const db = dbManager.getDatabase();
    
    // Get monthly list
    const MonthlyList = require('./MonthlyList');
    const monthlyList = MonthlyList.getByMonthYear(month, year);
    
    if (!monthlyList) {
      throw new Error('Monthly list not found');
    }

    // Get items from monthly list
    const itemsStmt = db.prepare(`
      SELECT i.id
      FROM items i
      JOIN list_items li ON i.id = li.item_id
      WHERE li.list_id = ?
    `);
    
    const items = itemsStmt.all(monthlyList.id);
    const itemIds = items.map(item => item.id);
    
    if (itemIds.length === 0) {
      throw new Error('No items found in monthly list');
    }

    return Template.createFromCurrentItems(name, description, itemIds);
  }

  static applyTemplateToCurrentMonth(templateId) {
    const db = dbManager.getDatabase();
    
    const template = Template.getById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Get current month/year
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();

    // Get or create monthly list for current month
    const MonthlyList = require('./MonthlyList');
    let monthlyList = MonthlyList.getByMonthYear(month, year);
    if (!monthlyList) {
      monthlyList = MonthlyList.create({ month, year });
    }

    // Get template items
    const templateItems = Template.getItemsForTemplate(templateId);
    
    if (templateItems.length === 0) {
      throw new Error('Template has no items');
    }

    // Add items to current month (skip duplicates)
    const addItemStmt = db.prepare(`
      INSERT OR IGNORE INTO list_items (list_id, item_id) VALUES (?, ?)
    `);
    
    let addedCount = 0;
    const transaction = db.transaction(() => {
      templateItems.forEach(item => {
        const result = addItemStmt.run(monthlyList.id, item.id);
        if (result.changes > 0) {
          addedCount++;
        }
      });
    });

    transaction();
    
    return {
      template,
      monthlyList,
      totalItems: templateItems.length,
      addedItems: addedCount,
      skippedItems: templateItems.length - addedCount
    };
  }

  static duplicateTemplate(templateId, newName) {
    const db = dbManager.getDatabase();
    
    const originalTemplate = Template.getById(templateId);
    if (!originalTemplate) {
      throw new Error('Template not found');
    }

    // Create new template
    const newTemplate = Template.create({
      name: newName,
      description: originalTemplate.description
    });

    // Copy items from original template
    const copyItemsStmt = db.prepare(`
      INSERT INTO template_items (template_id, item_id)
      SELECT ?, item_id FROM template_items WHERE template_id = ?
    `);
    
    copyItemsStmt.run(newTemplate.id, templateId);
    
    return newTemplate;
  }

  static getTemplateStatistics(templateId) {
    const db = dbManager.getDatabase();
    
    const statsStmt = db.prepare(`
      SELECT 
        COUNT(*) as total_items,
        SUM(i.price * i.quantity) as total_value,
        COUNT(DISTINCT i.category_id) as categories_count
      FROM items i
      JOIN template_items ti ON i.id = ti.item_id
      WHERE ti.template_id = ?
    `);
    
    const stats = statsStmt.get(templateId);
    
    return {
      totalItems: stats.total_items || 0,
      totalValue: stats.total_value || 0,
      categoriesCount: stats.categories_count || 0
    };
  }

  getItems() {
    return Template.getItemsForTemplate(this.id);
  }

  getStatistics() {
    return Template.getTemplateStatistics(this.id);
  }

  addItem(itemId) {
    return Template.addItemToTemplate(this.id, itemId);
  }

  removeItem(itemId) {
    return Template.removeItemFromTemplate(this.id, itemId);
  }

  applyToCurrentMonth() {
    return Template.applyTemplateToCurrentMonth(this.id);
  }

  duplicate(newName) {
    return Template.duplicateTemplate(this.id, newName);
  }

  save() {
    if (this.id) {
      return Template.update(this.id, {
        name: this.name,
        description: this.description
      });
    } else {
      return Template.create({
        name: this.name,
        description: this.description
      });
    }
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = Template;
