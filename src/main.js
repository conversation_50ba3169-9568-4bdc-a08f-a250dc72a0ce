const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const dbManager = require('./database/db');
const Category = require('./database/models/Category');
const Item = require('./database/models/Item');
const MonthlyList = require('./database/models/MonthlyList');
const Template = require('./database/models/Template');
const exportUtility = require('./utils/export');

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

class MainProcess {
  constructor() {
    this.mainWindow = null;
    this.isDev = process.env.NODE_ENV === 'development';
  }

  createWindow() {
    // Create the browser window with security best practices
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      icon: path.join(__dirname, 'assets', 'icon.png'), // Add icon later
      webPreferences: {
        nodeIntegration: false, // Security: disable node integration
        contextIsolation: true, // Security: enable context isolation
        enableRemoteModule: false, // Security: disable remote module
        preload: path.join(__dirname, 'preload.js'), // Secure IPC bridge
        webSecurity: true, // Security: enable web security
        allowRunningInsecureContent: false, // Security: block insecure content
        experimentalFeatures: false // Security: disable experimental features
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      show: false // Don't show until ready
    });

    // Load the app
    this.mainWindow.loadFile(path.join(__dirname, 'renderer', 'index.html'));

    // Show window when ready to prevent visual flash
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();

      // Open DevTools in development
      if (this.isDev) {
        this.mainWindow.webContents.openDevTools();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Security: Prevent new window creation
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // Security: Prevent navigation to external URLs
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl);

      if (parsedUrl.origin !== 'file://') {
        event.preventDefault();
      }
    });
  }

  setupIPC() {
    // Category IPC handlers
    ipcMain.handle('categories:getAll', async () => {
      try {
        return Category.getAll();
      } catch (error) {
        console.error('Error getting categories:', error);
        throw error;
      }
    });

    ipcMain.handle('categories:create', async (event, data) => {
      try {
        return Category.create(data);
      } catch (error) {
        console.error('Error creating category:', error);
        throw error;
      }
    });

    ipcMain.handle('categories:update', async (event, id, data) => {
      try {
        return Category.update(id, data);
      } catch (error) {
        console.error('Error updating category:', error);
        throw error;
      }
    });

    ipcMain.handle('categories:delete', async (event, id) => {
      try {
        return Category.delete(id);
      } catch (error) {
        console.error('Error deleting category:', error);
        throw error;
      }
    });

    ipcMain.handle('categories:reorder', async (event, categoryIds) => {
      try {
        return Category.reorder(categoryIds);
      } catch (error) {
        console.error('Error reordering categories:', error);
        throw error;
      }
    });

    ipcMain.handle('categories:reassignItems', async (event, fromId, toId) => {
      try {
        return Category.reassignItems(fromId, toId);
      } catch (error) {
        console.error('Error reassigning items:', error);
        throw error;
      }
    });

    // Item IPC handlers
    ipcMain.handle('items:getAll', async (event, filters) => {
      try {
        return Item.getAll(filters);
      } catch (error) {
        console.error('Error getting items:', error);
        throw error;
      }
    });

    ipcMain.handle('items:getById', async (event, id) => {
      try {
        return Item.getById(id);
      } catch (error) {
        console.error('Error getting item:', error);
        throw error;
      }
    });

    ipcMain.handle('items:create', async (event, data) => {
      try {
        return Item.create(data);
      } catch (error) {
        console.error('Error creating item:', error);
        throw error;
      }
    });

    ipcMain.handle('items:update', async (event, id, data) => {
      try {
        return Item.update(id, data);
      } catch (error) {
        console.error('Error updating item:', error);
        throw error;
      }
    });

    ipcMain.handle('items:delete', async (event, id) => {
      try {
        return Item.delete(id);
      } catch (error) {
        console.error('Error deleting item:', error);
        throw error;
      }
    });

    ipcMain.handle('items:deleteMultiple', async (event, ids) => {
      try {
        return Item.deleteMultiple(ids);
      } catch (error) {
        console.error('Error deleting multiple items:', error);
        throw error;
      }
    });

    ipcMain.handle('items:markPurchased', async (event, id, purchased) => {
      try {
        return Item.markPurchased(id, purchased);
      } catch (error) {
        console.error('Error marking item purchased:', error);
        throw error;
      }
    });

    ipcMain.handle('items:markMultiplePurchased', async (event, ids, purchased) => {
      try {
        return Item.markMultiplePurchased(ids, purchased);
      } catch (error) {
        console.error('Error marking multiple items purchased:', error);
        throw error;
      }
    });

    ipcMain.handle('items:getStatistics', async () => {
      try {
        return Item.getStatistics();
      } catch (error) {
        console.error('Error getting statistics:', error);
        throw error;
      }
    });

    // Monthly List IPC handlers
    ipcMain.handle('monthlyLists:getAll', async () => {
      try {
        return MonthlyList.getAll();
      } catch (error) {
        console.error('Error getting monthly lists:', error);
        throw error;
      }
    });

    ipcMain.handle('monthlyLists:getByMonthYear', async (event, month, year) => {
      try {
        return MonthlyList.getByMonthYear(month, year);
      } catch (error) {
        console.error('Error getting monthly list:', error);
        throw error;
      }
    });

    ipcMain.handle('monthlyLists:getItems', async (event, month, year) => {
      try {
        return MonthlyList.getItemsForMonth(month, year);
      } catch (error) {
        console.error('Error getting monthly list items:', error);
        throw error;
      }
    });

    ipcMain.handle('monthlyLists:getStatistics', async (event, month, year) => {
      try {
        return MonthlyList.getStatisticsForMonth(month, year);
      } catch (error) {
        console.error('Error getting monthly list statistics:', error);
        throw error;
      }
    });

    ipcMain.handle('monthlyLists:addItemToMonth', async (event, itemId, month, year) => {
      try {
        return MonthlyList.addItemToMonth(itemId, month, year);
      } catch (error) {
        console.error('Error adding item to monthly list:', error);
        throw error;
      }
    });

    ipcMain.handle('monthlyLists:compareMonths', async (event, month1, year1, month2, year2) => {
      try {
        return MonthlyList.compareMonths(month1, year1, month2, year2);
      } catch (error) {
        console.error('Error comparing months:', error);
        throw error;
      }
    });

    // Template IPC handlers
    ipcMain.handle('templates:getAll', async () => {
      try {
        return Template.getAll();
      } catch (error) {
        console.error('Error getting templates:', error);
        throw error;
      }
    });

    ipcMain.handle('templates:create', async (event, data) => {
      try {
        return Template.create(data);
      } catch (error) {
        console.error('Error creating template:', error);
        throw error;
      }
    });

    ipcMain.handle('templates:update', async (event, id, data) => {
      try {
        return Template.update(id, data);
      } catch (error) {
        console.error('Error updating template:', error);
        throw error;
      }
    });

    ipcMain.handle('templates:delete', async (event, id) => {
      try {
        return Template.delete(id);
      } catch (error) {
        console.error('Error deleting template:', error);
        throw error;
      }
    });

    ipcMain.handle('templates:applyToCurrentMonth', async (event, templateId) => {
      try {
        return Template.applyTemplateToCurrentMonth(templateId);
      } catch (error) {
        console.error('Error applying template:', error);
        throw error;
      }
    });

    ipcMain.handle('templates:createFromCurrentItems', async (event, name, description, itemIds) => {
      try {
        return Template.createFromCurrentItems(name, description, itemIds);
      } catch (error) {
        console.error('Error creating template from items:', error);
        throw error;
      }
    });

    // Export/Import IPC handlers
    ipcMain.handle('export:toJSON', async (event, items, filePath, options) => {
      try {
        return await exportUtility.exportToJSON(items, filePath, options);
      } catch (error) {
        console.error('Error exporting to JSON:', error);
        throw error;
      }
    });

    ipcMain.handle('export:toCSV', async (event, items, filePath, options) => {
      try {
        return await exportUtility.exportToCSV(items, filePath, options);
      } catch (error) {
        console.error('Error exporting to CSV:', error);
        throw error;
      }
    });

    ipcMain.handle('export:toExcel', async (event, items, filePath, options) => {
      try {
        return await exportUtility.exportToExcel(items, filePath, options);
      } catch (error) {
        console.error('Error exporting to Excel:', error);
        throw error;
      }
    });

    ipcMain.handle('export:toPDF', async (event, items, filePath, options) => {
      try {
        return await exportUtility.exportToPDF(items, filePath, options);
      } catch (error) {
        console.error('Error exporting to PDF:', error);
        throw error;
      }
    });

    ipcMain.handle('export:toJPG', async (event, items, filePath, options) => {
      try {
        return await exportUtility.exportToJPG(items, filePath, options);
      } catch (error) {
        console.error('Error exporting to JPG:', error);
        throw error;
      }
    });

    ipcMain.handle('export:importFile', async (event, filePath, options) => {
      try {
        let result;
        const fileExtension = path.extname(filePath).toLowerCase();

        if (fileExtension === '.json') {
          result = await exportUtility.importFromJSON(filePath);
        } else if (fileExtension === '.csv') {
          result = await exportUtility.importFromCSV(filePath);
        } else {
          throw new Error('Unsupported file format');
        }

        if (!result.success) {
          throw new Error(result.error || 'Import failed');
        }

        // Validate data if requested
        let validationResult = { valid: true, validItems: result.items, errors: [] };
        if (options.validateData) {
          validationResult = exportUtility.validateImportData(result.items);
        }

        // Process valid items
        const itemsToImport = validationResult.validItems;
        let importedCount = 0;
        let skippedCount = 0;

        for (const itemData of itemsToImport) {
          try {
            // Find or create category
            let category = await Category.getByName(itemData.category_name);
            if (!category && options.createCategories) {
              category = await Category.create({ name: itemData.category_name });
            }

            if (!category) {
              skippedCount++;
              continue;
            }

            // Check for duplicates if requested
            if (options.skipDuplicates) {
              const existingItems = await Item.getAll({ search: itemData.name });
              const duplicate = existingItems.find(item =>
                item.name.toLowerCase() === itemData.name.toLowerCase() &&
                item.category_id === category.id
              );

              if (duplicate) {
                skippedCount++;
                continue;
              }
            }

            // Create item
            await Item.create({
              ...itemData,
              category_id: category.id
            });

            importedCount++;
          } catch (error) {
            console.error('Error importing item:', itemData.name, error);
            skippedCount++;
          }
        }

        return {
          success: true,
          importedCount,
          skippedCount,
          totalItems: result.items.length,
          validationErrors: validationResult.errors
        };
      } catch (error) {
        console.error('Error importing file:', error);
        throw error;
      }
    });

    // Database IPC handlers
    ipcMain.handle('database:backup', async () => {
      try {
        return dbManager.createBackup();
      } catch (error) {
        console.error('Error creating backup:', error);
        throw error;
      }
    });

    // Dialog IPC handlers
    ipcMain.handle('dialog:showError', async (event, title, content) => {
      dialog.showErrorBox(title, content);
    });

    ipcMain.handle('dialog:showMessage', async (event, options) => {
      return dialog.showMessageBox(this.mainWindow, options);
    });

    ipcMain.handle('dialog:showSaveDialog', async (event, options) => {
      return dialog.showSaveDialog(this.mainWindow, options);
    });

    ipcMain.handle('dialog:showOpenDialog', async (event, options) => {
      return dialog.showOpenDialog(this.mainWindow, options);
    });
  }

  async initialize() {
    try {
      // Initialize database
      await dbManager.initialize();
      console.log('Database initialized successfully');

      // Setup IPC handlers
      this.setupIPC();
      console.log('IPC handlers setup complete');

      return true;
    } catch (error) {
      console.error('Initialization failed:', error);

      // Show error dialog
      dialog.showErrorBox(
        'Initialization Error',
        `Failed to initialize the application: ${error.message}`
      );

      app.quit();
      return false;
    }
  }
}

// Create main process instance
const mainProcess = new MainProcess();

// App event handlers
app.whenReady().then(async () => {
  // Initialize the application
  const initialized = await mainProcess.initialize();

  if (initialized) {
    // Create main window
    mainProcess.createWindow();
  }

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      mainProcess.createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // Close database connection
  dbManager.close();

  // Quit app (except on macOS)
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  // Clean up before quitting
  dbManager.close();
});

// Security: Prevent new window creation from renderer
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});