const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Category API
  categories: {
    getAll: () => ipcRenderer.invoke('categories:getAll'),
    create: (data) => ipcRenderer.invoke('categories:create', data),
    update: (id, data) => ipcRenderer.invoke('categories:update', id, data),
    delete: (id) => ipcRenderer.invoke('categories:delete', id),
    reorder: (categoryIds) => ipcRenderer.invoke('categories:reorder', categoryIds),
    reassignItems: (fromId, toId) => ipcRenderer.invoke('categories:reassignItems', fromId, toId)
  },

  // Item API
  items: {
    getAll: (filters) => ipcRenderer.invoke('items:getAll', filters),
    getById: (id) => ipc<PERSON>enderer.invoke('items:getById', id),
    create: (data) => ipcRenderer.invoke('items:create', data),
    update: (id, data) => ipcR<PERSON>er.invoke('items:update', id, data),
    delete: (id) => ipcRenderer.invoke('items:delete', id),
    deleteMultiple: (ids) => ipcRenderer.invoke('items:deleteMultiple', ids),
    markPurchased: (id, purchased) => ipcRenderer.invoke('items:markPurchased', id, purchased),
    markMultiplePurchased: (ids, purchased) => ipcRenderer.invoke('items:markMultiplePurchased', ids, purchased),
    getStatistics: () => ipcRenderer.invoke('items:getStatistics')
  },

  // Monthly Lists API
  monthlyLists: {
    getAll: () => ipcRenderer.invoke('monthlyLists:getAll'),
    getByMonthYear: (month, year) => ipcRenderer.invoke('monthlyLists:getByMonthYear', month, year),
    getItems: (month, year) => ipcRenderer.invoke('monthlyLists:getItems', month, year),
    getStatistics: (month, year) => ipcRenderer.invoke('monthlyLists:getStatistics', month, year),
    addItemToMonth: (itemId, month, year) => ipcRenderer.invoke('monthlyLists:addItemToMonth', itemId, month, year),
    compareMonths: (month1, year1, month2, year2) => ipcRenderer.invoke('monthlyLists:compareMonths', month1, year1, month2, year2)
  },

  // Templates API
  templates: {
    getAll: () => ipcRenderer.invoke('templates:getAll'),
    create: (data) => ipcRenderer.invoke('templates:create', data),
    update: (id, data) => ipcRenderer.invoke('templates:update', id, data),
    delete: (id) => ipcRenderer.invoke('templates:delete', id),
    applyToCurrentMonth: (templateId) => ipcRenderer.invoke('templates:applyToCurrentMonth', templateId),
    createFromCurrentItems: (name, description, itemIds) => ipcRenderer.invoke('templates:createFromCurrentItems', name, description, itemIds)
  },

  // Export/Import API
  export: {
    toJSON: (items, filePath, options) => ipcRenderer.invoke('export:toJSON', items, filePath, options),
    toCSV: (items, filePath, options) => ipcRenderer.invoke('export:toCSV', items, filePath, options),
    toExcel: (items, filePath, options) => ipcRenderer.invoke('export:toExcel', items, filePath, options),
    toPDF: (items, filePath, options) => ipcRenderer.invoke('export:toPDF', items, filePath, options),
    toJPG: (items, filePath, options) => ipcRenderer.invoke('export:toJPG', items, filePath, options),
    importFile: (filePath, options) => ipcRenderer.invoke('export:importFile', filePath, options)
  },

  // Database API
  database: {
    backup: () => ipcRenderer.invoke('database:backup')
  },

  // Dialog API
  dialog: {
    showError: (title, content) => ipcRenderer.invoke('dialog:showError', title, content),
    showMessage: (options) => ipcRenderer.invoke('dialog:showMessage', options),
    showSaveDialog: (options) => ipcRenderer.invoke('dialog:showSaveDialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('dialog:showOpenDialog', options)
  },

  // Utility functions
  utils: {
    // Format currency
    formatCurrency: (amount, currency = 'BDT') => {
      return new Intl.NumberFormat('en-BD', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(amount);
    },

    // Format date
    formatDate: (date, format = 'short') => {
      const dateObj = new Date(date);
      const options = {
        short: { year: 'numeric', month: 'short', day: 'numeric' },
        long: { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' },
        numeric: { year: 'numeric', month: '2-digit', day: '2-digit' }
      };
      
      return new Intl.DateTimeFormat('en-US', options[format] || options.short).format(dateObj);
    },

    // Validate email
    validateEmail: (email) => {
      const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return re.test(email);
    },

    // Generate UUID
    generateUUID: () => {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },

    // Debounce function
    debounce: (func, wait) => {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // Throttle function
    throttle: (func, limit) => {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    // Deep clone object
    deepClone: (obj) => {
      return JSON.parse(JSON.stringify(obj));
    },

    // Sanitize HTML
    sanitizeHTML: (str) => {
      const temp = document.createElement('div');
      temp.textContent = str;
      return temp.innerHTML;
    },

    // Calculate percentage
    calculatePercentage: (value, total) => {
      if (total === 0) return 0;
      return Math.round((value / total) * 100);
    },

    // Format file size
    formatFileSize: (bytes) => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // Capitalize first letter
    capitalize: (str) => {
      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    },

    // Truncate text
    truncate: (str, length = 50) => {
      if (str.length <= length) return str;
      return str.substring(0, length) + '...';
    },

    // Check if object is empty
    isEmpty: (obj) => {
      return Object.keys(obj).length === 0;
    },

    // Get current timestamp
    getCurrentTimestamp: () => {
      return new Date().toISOString();
    },

    // Parse query string
    parseQueryString: (queryString) => {
      const params = {};
      const pairs = queryString.substring(1).split('&');
      
      for (let i = 0; i < pairs.length; i++) {
        const pair = pairs[i].split('=');
        if (pair.length === 2) {
          params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
        }
      }
      
      return params;
    },

    // Build query string
    buildQueryString: (params) => {
      const pairs = [];
      for (const key in params) {
        if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined) {
          pairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
        }
      }
      return pairs.length > 0 ? '?' + pairs.join('&') : '';
    }
  },

  // Constants
  constants: {
    UNITS: ['Kg', 'g', 'L', 'mL', 'piece', 'dozen', 'pack'],
    MAX_ITEM_NAME_LENGTH: 100,
    MAX_BRAND_LENGTH: 50,
    MAX_REMARKS_LENGTH: 200,
    MAX_CATEGORY_NAME_LENGTH: 30,
    DEFAULT_CATEGORIES: [
      'Vegetables',
      'Fruits', 
      'Grains & Cereals',
      'Dairy & Eggs',
      'Meat & Seafood',
      'Beverages',
      'Snacks',
      'Household Items',
      'Personal Care'
    ]
  }
});

// Expose version info
contextBridge.exposeInMainWorld('appInfo', {
  version: process.env.npm_package_version || '1.0.0',
  platform: process.platform,
  arch: process.arch,
  electronVersion: process.versions.electron,
  nodeVersion: process.versions.node,
  chromeVersion: process.versions.chrome
});

// Security: Remove any global Node.js APIs that might have been exposed
delete window.require;
delete window.exports;
delete window.module;
