// Category Management Component
class CategoryManager {
  constructor(app) {
    this.app = app;
    this.draggedElement = null;
    this.draggedCategoryId = null;
  }

  showAddCategoryModal() {
    const modal = this.createCategoryModal();
    document.getElementById('modal-container').appendChild(modal);
    
    // Focus on name input
    setTimeout(() => {
      document.getElementById('category-name').focus();
    }, 100);
  }

  showEditCategoryModal(categoryId) {
    const category = this.app.categories.find(c => c.id === categoryId);
    if (!category) return;

    const modal = this.createCategoryModal(category);
    document.getElementById('modal-container').appendChild(modal);
    
    // Focus on name input
    setTimeout(() => {
      document.getElementById('category-name').focus();
    }, 100);
  }

  createCategoryModal(category = null) {
    const isEdit = category !== null;
    const modalDiv = document.createElement('div');
    modalDiv.className = 'modal-overlay';
    modalDiv.innerHTML = `
      <div class="modal-content">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            ${isEdit ? 'Edit Category' : 'Add New Category'}
          </h3>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.closest('.modal-overlay').remove()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <form id="category-form" class="space-y-4">
          <div>
            <label for="category-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Category Name *
            </label>
            <input
              type="text"
              id="category-name"
              name="name"
              class="input-field"
              placeholder="Enter category name"
              value="${isEdit ? electronAPI.utils.sanitizeHTML(category.name) : ''}"
              maxlength="${electronAPI.constants.MAX_CATEGORY_NAME_LENGTH}"
              required
            />
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Maximum ${electronAPI.constants.MAX_CATEGORY_NAME_LENGTH} characters
            </p>
          </div>

          ${isEdit ? `
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Items in this category
              </label>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                ${this.app.items.filter(item => item.category_id === category.id).length} items
              </div>
            </div>
          ` : ''}

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              class="btn-secondary"
              onclick="this.closest('.modal-overlay').remove()"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="btn-primary"
            >
              ${isEdit ? 'Update Category' : 'Add Category'}
            </button>
          </div>
        </form>
      </div>
    `;

    // Add form submit handler
    const form = modalDiv.querySelector('#category-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const formData = new FormData(form);
      const name = formData.get('name').trim();
      
      if (!name) {
        this.app.showToast('Category name is required', 'error');
        return;
      }

      try {
        if (isEdit) {
          await this.updateCategory(category.id, { name });
        } else {
          await this.createCategory({ name });
        }
        
        modalDiv.remove();
      } catch (error) {
        console.error('Category operation failed:', error);
        this.app.showToast(error.message || 'Operation failed', 'error');
      }
    });

    // Close modal on escape key
    modalDiv.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        modalDiv.remove();
      }
    });

    // Close modal on backdrop click
    modalDiv.addEventListener('click', (e) => {
      if (e.target === modalDiv) {
        modalDiv.remove();
      }
    });

    return modalDiv;
  }

  async createCategory(data) {
    try {
      const newCategory = await electronAPI.categories.create(data);
      
      // Add to local state
      this.app.categories.push(newCategory);
      this.app.categories.sort((a, b) => a.sort_order - b.sort_order);
      
      // Re-render categories
      this.app.renderCategories();
      
      this.app.showToast('Category created successfully', 'success');
      
      return newCategory;
    } catch (error) {
      console.error('Failed to create category:', error);
      throw error;
    }
  }

  async updateCategory(categoryId, data) {
    try {
      const updatedCategory = await electronAPI.categories.update(categoryId, data);
      
      // Update local state
      const index = this.app.categories.findIndex(c => c.id === categoryId);
      if (index !== -1) {
        this.app.categories[index] = updatedCategory;
      }
      
      // Re-render categories
      this.app.renderCategories();
      
      this.app.showToast('Category updated successfully', 'success');
      
      return updatedCategory;
    } catch (error) {
      console.error('Failed to update category:', error);
      throw error;
    }
  }

  async deleteCategory(categoryId) {
    const category = this.app.categories.find(c => c.id === categoryId);
    if (!category) return;

    const itemCount = this.app.items.filter(item => item.category_id === categoryId).length;
    
    if (itemCount > 0) {
      // Show reassignment dialog
      this.showReassignmentDialog(categoryId, itemCount);
      return;
    }

    const result = await electronAPI.dialog.showMessage({
      type: 'warning',
      buttons: ['Cancel', 'Delete'],
      defaultId: 0,
      title: 'Delete Category',
      message: `Are you sure you want to delete the category "${category.name}"?`,
      detail: 'This action cannot be undone.'
    });

    if (result.response === 1) {
      try {
        await electronAPI.categories.delete(categoryId);
        
        // Remove from local state
        this.app.categories = this.app.categories.filter(cat => cat.id !== categoryId);
        
        // Re-render
        this.app.renderCategories();
        
        this.app.showToast('Category deleted successfully', 'success');
      } catch (error) {
        console.error('Failed to delete category:', error);
        this.app.showToast('Failed to delete category', 'error');
      }
    }
  }

  showReassignmentDialog(categoryId, itemCount) {
    const category = this.app.categories.find(c => c.id === categoryId);
    const otherCategories = this.app.categories.filter(c => c.id !== categoryId);

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Reassign Items
          </h3>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.closest('.modal-overlay').remove()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="mb-4">
          <p class="text-gray-700 dark:text-gray-300">
            The category "${electronAPI.utils.sanitizeHTML(category.name)}" contains ${itemCount} items. 
            You need to reassign these items to another category before deleting.
          </p>
        </div>

        <form id="reassign-form" class="space-y-4">
          <div>
            <label for="target-category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Move items to:
            </label>
            <select id="target-category" name="targetCategory" class="input-field" required>
              <option value="">Select a category</option>
              ${otherCategories.map(cat => 
                `<option value="${cat.id}">${electronAPI.utils.sanitizeHTML(cat.name)}</option>`
              ).join('')}
            </select>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button type="button" class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">
              Cancel
            </button>
            <button type="submit" class="btn-primary">
              Reassign and Delete
            </button>
          </div>
        </form>
      </div>
    `;

    const form = modal.querySelector('#reassign-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const formData = new FormData(form);
      const targetCategoryId = parseInt(formData.get('targetCategory'));
      
      if (!targetCategoryId) {
        this.app.showToast('Please select a target category', 'error');
        return;
      }

      try {
        // Reassign items
        await electronAPI.categories.reassignItems(categoryId, targetCategoryId);
        
        // Update local state
        this.app.items.forEach(item => {
          if (item.category_id === categoryId) {
            item.category_id = targetCategoryId;
            const targetCategory = this.app.categories.find(c => c.id === targetCategoryId);
            if (targetCategory) {
              item.category_name = targetCategory.name;
            }
          }
        });

        // Delete the category
        await electronAPI.categories.delete(categoryId);
        
        // Remove from local state
        this.app.categories = this.app.categories.filter(cat => cat.id !== categoryId);
        
        // Re-render
        this.app.renderCategories();
        
        modal.remove();
        this.app.showToast(`Items reassigned and category deleted`, 'success');
      } catch (error) {
        console.error('Failed to reassign items:', error);
        this.app.showToast('Failed to reassign items', 'error');
      }
    });

    document.getElementById('modal-container').appendChild(modal);
  }

  enableDragAndDrop() {
    const categoriesContainer = document.getElementById('categories-container');
    
    // Add drag and drop event listeners
    categoriesContainer.addEventListener('dragstart', this.handleDragStart.bind(this));
    categoriesContainer.addEventListener('dragover', this.handleDragOver.bind(this));
    categoriesContainer.addEventListener('drop', this.handleDrop.bind(this));
    categoriesContainer.addEventListener('dragend', this.handleDragEnd.bind(this));
  }

  handleDragStart(e) {
    const categoryElement = e.target.closest('.category-section');
    if (!categoryElement) return;

    this.draggedElement = categoryElement;
    this.draggedCategoryId = this.extractCategoryId(categoryElement);
    
    categoryElement.style.opacity = '0.5';
    e.dataTransfer.effectAllowed = 'move';
  }

  handleDragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    const categoryElement = e.target.closest('.category-section');
    if (!categoryElement || categoryElement === this.draggedElement) return;

    // Add visual feedback
    categoryElement.classList.add('border-primary-500', 'border-2', 'border-dashed');
  }

  handleDrop(e) {
    e.preventDefault();
    
    const targetElement = e.target.closest('.category-section');
    if (!targetElement || targetElement === this.draggedElement) return;

    const targetCategoryId = this.extractCategoryId(targetElement);
    
    // Reorder categories
    this.reorderCategories(this.draggedCategoryId, targetCategoryId);
    
    // Remove visual feedback
    document.querySelectorAll('.category-section').forEach(el => {
      el.classList.remove('border-primary-500', 'border-2', 'border-dashed');
    });
  }

  handleDragEnd(e) {
    if (this.draggedElement) {
      this.draggedElement.style.opacity = '';
      this.draggedElement = null;
      this.draggedCategoryId = null;
    }
    
    // Remove all visual feedback
    document.querySelectorAll('.category-section').forEach(el => {
      el.classList.remove('border-primary-500', 'border-2', 'border-dashed');
    });
  }

  extractCategoryId(categoryElement) {
    const itemsContainer = categoryElement.querySelector('[id^="category-"][id$="-items"]');
    if (itemsContainer) {
      const match = itemsContainer.id.match(/category-(\d+)-items/);
      return match ? parseInt(match[1]) : null;
    }
    return null;
  }

  async reorderCategories(draggedId, targetId) {
    try {
      const draggedIndex = this.app.categories.findIndex(c => c.id === draggedId);
      const targetIndex = this.app.categories.findIndex(c => c.id === targetId);
      
      if (draggedIndex === -1 || targetIndex === -1) return;

      // Reorder locally first for immediate feedback
      const draggedCategory = this.app.categories[draggedIndex];
      this.app.categories.splice(draggedIndex, 1);
      this.app.categories.splice(targetIndex, 0, draggedCategory);

      // Update sort orders
      this.app.categories.forEach((category, index) => {
        category.sort_order = index + 1;
      });

      // Send to backend
      const categoryIds = this.app.categories.map(c => c.id);
      await electronAPI.categories.reorder(categoryIds);
      
      // Re-render
      this.app.renderCategories();
      
      this.app.showToast('Categories reordered', 'success');
    } catch (error) {
      console.error('Failed to reorder categories:', error);
      this.app.showToast('Failed to reorder categories', 'error');
      
      // Reload categories to restore original order
      await this.app.loadCategories();
    }
  }

  makeCategoriesDraggable() {
    document.querySelectorAll('.category-section').forEach(categoryElement => {
      categoryElement.draggable = true;
      categoryElement.style.cursor = 'move';
      
      // Add drag handle visual indicator
      const header = categoryElement.querySelector('.category-header');
      if (header && !header.querySelector('.drag-handle')) {
        const dragHandle = document.createElement('div');
        dragHandle.className = 'drag-handle text-gray-400 hover:text-gray-600 dark:hover:text-gray-200';
        dragHandle.innerHTML = `
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
          </svg>
        `;
        header.insertBefore(dragHandle, header.firstChild);
      }
    });
  }
}

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CategoryManager;
}
