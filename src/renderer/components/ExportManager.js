// Export Manager Component for handling data export functionality
class ExportManager {
  constructor(app) {
    this.app = app;
  }

  showExportModal() {
    const modal = this.createExportModal();
    document.getElementById('modal-container').appendChild(modal);
  }

  createExportModal() {
    const modalDiv = document.createElement('div');
    modalDiv.className = 'modal-overlay';
    modalDiv.innerHTML = `
      <div class="modal-content max-w-md">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Export Grocery List
          </h3>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.closest('.modal-overlay').remove()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <form id="export-form" class="space-y-4">
          <!-- Export Format -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Export Format
            </label>
            <div class="space-y-2">
              <label class="flex items-center space-x-2">
                <input type="radio" name="format" value="pdf" class="text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">PDF Document</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="radio" name="format" value="jpg" class="text-primary-600 focus:ring-primary-500">
                <span class="text-sm text-gray-700 dark:text-gray-300">JPG Image</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="radio" name="format" value="excel" class="text-primary-600 focus:ring-primary-500">
                <span class="text-sm text-gray-700 dark:text-gray-300">Excel Spreadsheet</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="radio" name="format" value="csv" class="text-primary-600 focus:ring-primary-500">
                <span class="text-sm text-gray-700 dark:text-gray-300">CSV File</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="radio" name="format" value="json" class="text-primary-600 focus:ring-primary-500">
                <span class="text-sm text-gray-700 dark:text-gray-300">JSON Data</span>
              </label>
            </div>
          </div>

          <!-- Export Options -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              What to Export
            </label>
            <div class="space-y-2">
              <label class="flex items-center space-x-2">
                <input type="radio" name="scope" value="all" class="text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">All Items</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="radio" name="scope" value="pending" class="text-primary-600 focus:ring-primary-500">
                <span class="text-sm text-gray-700 dark:text-gray-300">Pending Items Only</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="radio" name="scope" value="purchased" class="text-primary-600 focus:ring-primary-500">
                <span class="text-sm text-gray-700 dark:text-gray-300">Purchased Items Only</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="radio" name="scope" value="selected" class="text-primary-600 focus:ring-primary-500" ${this.app.selectedItems.size === 0 ? 'disabled' : ''}>
                <span class="text-sm text-gray-700 dark:text-gray-300">Selected Items (${this.app.selectedItems.size})</span>
              </label>
            </div>
          </div>

          <!-- Additional Options -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Additional Options
            </label>
            <div class="space-y-2">
              <label class="flex items-center space-x-2">
                <input type="checkbox" name="include_prices" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">Include Prices</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="checkbox" name="include_brands" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">Include Brand Names</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="checkbox" name="include_remarks" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                <span class="text-sm text-gray-700 dark:text-gray-300">Include Remarks</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="checkbox" name="group_by_category" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">Group by Category</span>
              </label>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              class="btn-secondary"
              onclick="this.closest('.modal-overlay').remove()"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="btn-primary"
            >
              Export
            </button>
          </div>
        </form>
      </div>
    `;

    // Add form submit handler
    const form = modalDiv.querySelector('#export-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const formData = new FormData(form);
      const exportOptions = {
        format: formData.get('format'),
        scope: formData.get('scope'),
        includePrices: formData.has('include_prices'),
        includeBrands: formData.has('include_brands'),
        includeRemarks: formData.has('include_remarks'),
        groupByCategory: formData.has('group_by_category')
      };
      
      try {
        await this.performExport(exportOptions);
        modalDiv.remove();
      } catch (error) {
        console.error('Export failed:', error);
        this.app.showToast('Export failed: ' + error.message, 'error');
      }
    });

    // Close modal on escape key
    modalDiv.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        modalDiv.remove();
      }
    });

    // Close modal on backdrop click
    modalDiv.addEventListener('click', (e) => {
      if (e.target === modalDiv) {
        modalDiv.remove();
      }
    });

    return modalDiv;
  }

  async performExport(options) {
    // Get items based on scope
    let itemsToExport = this.getItemsForExport(options.scope);
    
    if (itemsToExport.length === 0) {
      throw new Error('No items to export');
    }

    // Show save dialog
    const result = await electronAPI.dialog.showSaveDialog({
      title: 'Save Export File',
      defaultPath: this.getDefaultFileName(options.format),
      filters: this.getFileFilters(options.format)
    });

    if (result.canceled) {
      return;
    }

    // Show progress
    this.app.showToast('Exporting...', 'info');

    try {
      switch (options.format) {
        case 'pdf':
          await this.exportToPDF(itemsToExport, result.filePath, options);
          break;
        case 'jpg':
          await this.exportToJPG(itemsToExport, result.filePath, options);
          break;
        case 'excel':
          await this.exportToExcel(itemsToExport, result.filePath, options);
          break;
        case 'csv':
          await this.exportToCSV(itemsToExport, result.filePath, options);
          break;
        case 'json':
          await this.exportToJSON(itemsToExport, result.filePath, options);
          break;
        default:
          throw new Error('Unsupported export format');
      }

      this.app.showToast('Export completed successfully', 'success');
    } catch (error) {
      console.error('Export error:', error);
      throw error;
    }
  }

  getItemsForExport(scope) {
    switch (scope) {
      case 'all':
        return this.app.items;
      case 'pending':
        return this.app.items.filter(item => !item.purchased);
      case 'purchased':
        return this.app.items.filter(item => item.purchased);
      case 'selected':
        return this.app.items.filter(item => this.app.selectedItems.has(item.id));
      default:
        return this.app.items;
    }
  }

  getDefaultFileName(format) {
    const date = new Date().toISOString().split('T')[0];
    const month = new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    
    switch (format) {
      case 'pdf':
        return `Grocery_List_${month.replace(' ', '_')}_${date}.pdf`;
      case 'jpg':
        return `Grocery_List_${month.replace(' ', '_')}_${date}.jpg`;
      case 'excel':
        return `Grocery_List_${month.replace(' ', '_')}_${date}.xlsx`;
      case 'csv':
        return `Grocery_List_${month.replace(' ', '_')}_${date}.csv`;
      case 'json':
        return `Grocery_List_${month.replace(' ', '_')}_${date}.json`;
      default:
        return `Grocery_List_${date}`;
    }
  }

  getFileFilters(format) {
    switch (format) {
      case 'pdf':
        return [{ name: 'PDF Files', extensions: ['pdf'] }];
      case 'jpg':
        return [{ name: 'JPEG Images', extensions: ['jpg', 'jpeg'] }];
      case 'excel':
        return [{ name: 'Excel Files', extensions: ['xlsx'] }];
      case 'csv':
        return [{ name: 'CSV Files', extensions: ['csv'] }];
      case 'json':
        return [{ name: 'JSON Files', extensions: ['json'] }];
      default:
        return [{ name: 'All Files', extensions: ['*'] }];
    }
  }

  async exportToPDF(items, filePath, options) {
    try {
      const result = await electronAPI.export.toPDF(items, filePath, options);
      return result;
    } catch (error) {
      throw new Error(`PDF export failed: ${error.message}`);
    }
  }

  async exportToJPG(items, filePath, options) {
    try {
      const result = await electronAPI.export.toJPG(items, filePath, options);
      return result;
    } catch (error) {
      throw new Error(`JPG export failed: ${error.message}`);
    }
  }

  async exportToExcel(items, filePath, options) {
    try {
      const result = await electronAPI.export.toExcel(items, filePath, options);
      return result;
    } catch (error) {
      throw new Error(`Excel export failed: ${error.message}`);
    }
  }

  async exportToCSV(items, filePath, options) {
    try {
      const result = await electronAPI.export.toCSV(items, filePath, options);
      return result;
    } catch (error) {
      throw new Error(`CSV export failed: ${error.message}`);
    }
  }

  async exportToJSON(items, filePath, options) {
    try {
      const result = await electronAPI.export.toJSON(items, filePath, options);
      return result;
    } catch (error) {
      throw new Error(`JSON export failed: ${error.message}`);
    }
  }

  // Import functionality
  showImportModal() {
    const modal = this.createImportModal();
    document.getElementById('modal-container').appendChild(modal);
  }

  createImportModal() {
    const modalDiv = document.createElement('div');
    modalDiv.className = 'modal-overlay';
    modalDiv.innerHTML = `
      <div class="modal-content max-w-md">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Import Grocery Items
          </h3>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.closest('.modal-overlay').remove()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <form id="import-form" class="space-y-4">
          <!-- Import Format -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Import Format
            </label>
            <div class="space-y-2">
              <label class="flex items-center space-x-2">
                <input type="radio" name="format" value="json" class="text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">JSON File</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="radio" name="format" value="csv" class="text-primary-600 focus:ring-primary-500">
                <span class="text-sm text-gray-700 dark:text-gray-300">CSV File</span>
              </label>
            </div>
          </div>

          <!-- File Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select File
            </label>
            <button type="button" id="select-file-btn" class="w-full btn-secondary text-left">
              <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              Choose file to import...
            </button>
            <input type="file" id="file-input" class="hidden" accept=".json,.csv">
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1" id="file-info">
              No file selected
            </p>
          </div>

          <!-- Import Options -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Import Options
            </label>
            <div class="space-y-2">
              <label class="flex items-center space-x-2">
                <input type="checkbox" name="validate_data" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">Validate data before import</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="checkbox" name="skip_duplicates" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">Skip duplicate items</span>
              </label>
              <label class="flex items-center space-x-2">
                <input type="checkbox" name="create_categories" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" checked>
                <span class="text-sm text-gray-700 dark:text-gray-300">Create missing categories</span>
              </label>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              class="btn-secondary"
              onclick="this.closest('.modal-overlay').remove()"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="btn-primary"
              disabled
            >
              Import Items
            </button>
          </div>
        </form>
      </div>
    `;

    // Add event listeners
    this.setupImportEventListeners(modalDiv);

    return modalDiv;
  }

  setupImportEventListeners(modal) {
    const fileInput = modal.querySelector('#file-input');
    const selectFileBtn = modal.querySelector('#select-file-btn');
    const fileInfo = modal.querySelector('#file-info');
    const submitBtn = modal.querySelector('button[type="submit"]');
    const form = modal.querySelector('#import-form');

    // File selection
    selectFileBtn.addEventListener('click', () => {
      fileInput.click();
    });

    fileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        fileInfo.textContent = `Selected: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
        submitBtn.disabled = false;
      } else {
        fileInfo.textContent = 'No file selected';
        submitBtn.disabled = true;
      }
    });

    // Form submission
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      await this.performImport(modal);
    });

    // Close modal on escape key
    modal.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        modal.remove();
      }
    });

    // Close modal on backdrop click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  async performImport(modal) {
    const formData = new FormData(modal.querySelector('#import-form'));
    const fileInput = modal.querySelector('#file-input');
    const file = fileInput.files[0];

    if (!file) {
      this.app.showToast('Please select a file to import', 'error');
      return;
    }

    const options = {
      format: formData.get('format'),
      validateData: formData.has('validate_data'),
      skipDuplicates: formData.has('skip_duplicates'),
      createCategories: formData.has('create_categories')
    };

    try {
      // Show progress
      this.app.showToast('Importing items...', 'info');

      // Read file and import
      const result = await electronAPI.export.importFile(file.path, options);

      if (result.success) {
        modal.remove();

        // Refresh data
        await this.app.loadCategories();
        await this.app.loadItems();

        this.app.showToast(
          `Import completed: ${result.importedCount} items imported${result.skippedCount > 0 ? `, ${result.skippedCount} skipped` : ''}`,
          'success'
        );

        // Show validation errors if any
        if (result.validationErrors && result.validationErrors.length > 0) {
          this.showValidationErrorsModal(result.validationErrors);
        }
      } else {
        throw new Error(result.error || 'Import failed');
      }
    } catch (error) {
      console.error('Import failed:', error);
      this.app.showToast('Import failed: ' + error.message, 'error');
    }
  }

  showValidationErrorsModal(errors) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content max-w-2xl">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Import Validation Errors
          </h3>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.closest('.modal-overlay').remove()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="mb-4">
          <p class="text-gray-700 dark:text-gray-300">
            The following items had validation errors and were not imported:
          </p>
        </div>

        <div class="max-h-96 overflow-y-auto space-y-3">
          ${errors.map(error => `
            <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-3">
              <div class="font-medium text-red-800 dark:text-red-200">
                Row ${error.row}: ${electronAPI.utils.sanitizeHTML(error.item)}
              </div>
              <ul class="text-sm text-red-700 dark:text-red-300 mt-1 ml-4">
                ${error.errors.map(err => `<li>• ${electronAPI.utils.sanitizeHTML(err)}</li>`).join('')}
              </ul>
            </div>
          `).join('')}
        </div>

        <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
          <button class="btn-primary" onclick="this.closest('.modal-overlay').remove()">
            Close
          </button>
        </div>
      </div>
    `;

    document.getElementById('modal-container').appendChild(modal);
  }

  // Quick export methods
  async quickExportPDF() {
    const options = {
      format: 'pdf',
      scope: 'all',
      includePrices: true,
      includeBrands: true,
      includeRemarks: false,
      groupByCategory: true
    };

    await this.performExport(options);
  }

  async quickExportExcel() {
    const options = {
      format: 'excel',
      scope: 'all',
      includePrices: true,
      includeBrands: true,
      includeRemarks: true,
      groupByCategory: true
    };

    await this.performExport(options);
  }
}

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ExportManager;
}
