// History View Component for monthly list management and templates
class HistoryView {
  constructor(app) {
    this.app = app;
    this.currentYear = new Date().getFullYear();
    this.selectedMonth = null;
    this.selectedYear = null;
  }

  showHistoryModal() {
    const modal = this.createHistoryModal();
    document.getElementById('modal-container').appendChild(modal);
    this.loadHistoryData();
  }

  createHistoryModal() {
    const modalDiv = document.createElement('div');
    modalDiv.className = 'modal-overlay';
    modalDiv.innerHTML = `
      <div class="modal-content max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <div class="flex items-center justify-between mb-4 flex-shrink-0">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Monthly History & Templates
          </h3>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.closest('.modal-overlay').remove()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="flex flex-1 overflow-hidden">
          <!-- Sidebar -->
          <div class="w-80 border-r border-gray-200 dark:border-gray-700 pr-4 flex flex-col">
            <!-- Year Navigation -->
            <div class="flex items-center justify-between mb-4">
              <button id="prev-year" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
              </button>
              <h4 id="current-year" class="text-lg font-medium text-gray-900 dark:text-gray-100">${this.currentYear}</h4>
              <button id="next-year" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </button>
            </div>

            <!-- Calendar Grid -->
            <div class="grid grid-cols-3 gap-2 mb-6">
              ${this.generateCalendarGrid()}
            </div>

            <!-- Templates Section -->
            <div class="flex-1 overflow-hidden">
              <div class="flex items-center justify-between mb-3">
                <h5 class="font-medium text-gray-900 dark:text-gray-100">Templates</h5>
                <button id="create-template-btn" class="btn-primary text-xs py-1 px-2">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                  </svg>
                  New
                </button>
              </div>
              <div id="templates-list" class="overflow-y-auto max-h-64 space-y-2">
                <!-- Templates will be loaded here -->
              </div>
            </div>
          </div>

          <!-- Main Content -->
          <div class="flex-1 pl-4 overflow-hidden flex flex-col">
            <div id="history-content" class="flex-1 overflow-y-auto">
              <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <p>Select a month to view its grocery list</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add event listeners
    this.setupHistoryEventListeners(modalDiv);

    return modalDiv;
  }

  generateCalendarGrid() {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return months.map((month, index) => {
      const monthNumber = index + 1;
      return `
        <button 
          class="month-btn p-3 text-sm rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          data-month="${monthNumber}"
          data-year="${this.currentYear}"
        >
          <div class="font-medium">${month}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400 month-stats" data-month="${monthNumber}">
            <!-- Stats will be loaded -->
          </div>
        </button>
      `;
    }).join('');
  }

  setupHistoryEventListeners(modal) {
    // Year navigation
    modal.querySelector('#prev-year').addEventListener('click', () => {
      this.currentYear--;
      this.updateYearDisplay(modal);
      this.loadMonthStats(modal);
    });

    modal.querySelector('#next-year').addEventListener('click', () => {
      this.currentYear++;
      this.updateYearDisplay(modal);
      this.loadMonthStats(modal);
    });

    // Month selection
    modal.addEventListener('click', (e) => {
      if (e.target.closest('.month-btn')) {
        const btn = e.target.closest('.month-btn');
        const month = parseInt(btn.dataset.month);
        const year = parseInt(btn.dataset.year);
        
        // Update selection
        modal.querySelectorAll('.month-btn').forEach(b => {
          b.classList.remove('bg-primary-600', 'text-white');
        });
        btn.classList.add('bg-primary-600', 'text-white');
        
        this.selectedMonth = month;
        this.selectedYear = year;
        this.loadMonthDetails(modal, month, year);
      }
    });

    // Create template button
    modal.querySelector('#create-template-btn').addEventListener('click', () => {
      this.showCreateTemplateModal();
    });

    // Close modal on escape key
    modal.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        modal.remove();
      }
    });

    // Close modal on backdrop click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  updateYearDisplay(modal) {
    modal.querySelector('#current-year').textContent = this.currentYear;
    
    // Update month buttons with new year
    modal.querySelectorAll('.month-btn').forEach(btn => {
      btn.dataset.year = this.currentYear;
    });
  }

  async loadHistoryData() {
    try {
      await this.loadMonthStats();
      await this.loadTemplates();
    } catch (error) {
      console.error('Failed to load history data:', error);
      this.app.showToast('Failed to load history data', 'error');
    }
  }

  async loadMonthStats(modal = null) {
    if (!modal) {
      modal = document.querySelector('.modal-overlay');
    }
    
    try {
      // Load stats for each month of the current year
      for (let month = 1; month <= 12; month++) {
        const stats = await electronAPI.monthlyLists.getStatistics(month, this.currentYear);
        const statsElement = modal.querySelector(`.month-stats[data-month="${month}"]`);
        
        if (statsElement) {
          if (stats.totalItems > 0) {
            statsElement.innerHTML = `${stats.totalItems} items`;
            statsElement.parentElement.classList.add('border-primary-300', 'bg-primary-50', 'dark:bg-primary-900');
          } else {
            statsElement.innerHTML = '';
            statsElement.parentElement.classList.remove('border-primary-300', 'bg-primary-50', 'dark:bg-primary-900');
          }
        }
      }
    } catch (error) {
      console.error('Failed to load month stats:', error);
    }
  }

  async loadMonthDetails(modal, month, year) {
    const contentDiv = modal.querySelector('#history-content');
    contentDiv.innerHTML = '<div class="text-center py-8">Loading...</div>';

    try {
      const monthlyList = await electronAPI.monthlyLists.getByMonthYear(month, year);
      const items = await electronAPI.monthlyLists.getItems(month, year);
      const stats = await electronAPI.monthlyLists.getStatistics(month, year);

      const monthName = new Date(year, month - 1).toLocaleDateString('en-US', { 
        month: 'long', 
        year: 'numeric' 
      });

      contentDiv.innerHTML = `
        <div class="space-y-6">
          <!-- Header -->
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">${monthName}</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                ${stats.totalItems} items • ${stats.purchasedItems} purchased
              </p>
            </div>
            <div class="flex space-x-2">
              <button class="btn-secondary text-sm" onclick="historyView.compareWithMonth(${month}, ${year})">
                Compare
              </button>
              <button class="btn-secondary text-sm" onclick="historyView.createTemplateFromMonth(${month}, ${year})">
                Save as Template
              </button>
              <button class="btn-primary text-sm" onclick="historyView.copyToCurrentMonth(${month}, ${year})">
                Copy to Current
              </button>
            </div>
          </div>

          <!-- Statistics -->
          <div class="grid grid-cols-4 gap-4">
            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div class="text-sm text-gray-600 dark:text-gray-400">Total Items</div>
              <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">${stats.totalItems}</div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div class="text-sm text-gray-600 dark:text-gray-400">Purchased</div>
              <div class="text-lg font-semibold text-green-600">${stats.purchasedItems}</div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div class="text-sm text-gray-600 dark:text-gray-400">Total Value</div>
              <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">${electronAPI.utils.formatCurrency(stats.totalValue)}</div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div class="text-sm text-gray-600 dark:text-gray-400">Budget</div>
              <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">${electronAPI.utils.formatCurrency(stats.budget)}</div>
            </div>
          </div>

          <!-- Items List -->
          <div class="space-y-4">
            ${this.renderMonthItems(items)}
          </div>
        </div>
      `;
    } catch (error) {
      console.error('Failed to load month details:', error);
      contentDiv.innerHTML = `
        <div class="text-center py-8 text-red-600">
          Failed to load month details
        </div>
      `;
    }
  }

  renderMonthItems(items) {
    if (items.length === 0) {
      return '<div class="text-center py-8 text-gray-500 dark:text-gray-400">No items found</div>';
    }

    // Group items by category
    const itemsByCategory = {};
    items.forEach(item => {
      const categoryName = item.category_name || 'Uncategorized';
      if (!itemsByCategory[categoryName]) {
        itemsByCategory[categoryName] = [];
      }
      itemsByCategory[categoryName].push(item);
    });

    return Object.entries(itemsByCategory).map(([categoryName, categoryItems]) => `
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h5 class="font-medium text-gray-900 dark:text-gray-100">${electronAPI.utils.sanitizeHTML(categoryName)}</h5>
          <p class="text-sm text-gray-600 dark:text-gray-400">${categoryItems.length} items</p>
        </div>
        <div class="p-4 space-y-2">
          ${categoryItems.map(item => `
            <div class="flex items-center justify-between py-2 ${item.purchased ? 'opacity-60 line-through' : ''}">
              <div class="flex-1">
                <div class="font-medium text-gray-900 dark:text-gray-100">${electronAPI.utils.sanitizeHTML(item.name)}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  ${item.quantity} ${item.unit}
                  ${item.brand ? ` • ${electronAPI.utils.sanitizeHTML(item.brand)}` : ''}
                  ${item.price > 0 ? ` • ${electronAPI.utils.formatCurrency(item.price)}` : ''}
                </div>
              </div>
              <div class="flex items-center space-x-2">
                ${item.purchased ? 
                  '<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
                  '<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
                }
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `).join('');
  }

  async loadTemplates() {
    const templatesContainer = document.getElementById('templates-list');
    if (!templatesContainer) return;

    try {
      const templates = await electronAPI.templates.getAll();
      
      if (templates.length === 0) {
        templatesContainer.innerHTML = `
          <div class="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
            No templates found
          </div>
        `;
        return;
      }

      templatesContainer.innerHTML = templates.map(template => `
        <div class="template-item bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <div class="flex items-center justify-between">
            <div class="flex-1 min-w-0">
              <h6 class="font-medium text-gray-900 dark:text-gray-100 truncate">${electronAPI.utils.sanitizeHTML(template.name)}</h6>
              <p class="text-xs text-gray-600 dark:text-gray-400 truncate">${electronAPI.utils.sanitizeHTML(template.description || 'No description')}</p>
            </div>
            <div class="flex items-center space-x-1 ml-2">
              <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" onclick="historyView.applyTemplate(${template.id})" title="Apply template">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
              </button>
              <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" onclick="historyView.editTemplate(${template.id})" title="Edit template">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
              </button>
              <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-red-600" onclick="historyView.deleteTemplate(${template.id})" title="Delete template">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      `).join('');
    } catch (error) {
      console.error('Failed to load templates:', error);
      templatesContainer.innerHTML = `
        <div class="text-center py-4 text-red-600 text-sm">
          Failed to load templates
        </div>
      `;
    }
  }

  // Template and history action methods
  showCreateTemplateModal() {
    // TODO: Implement create template modal
    console.log('Show create template modal');
  }

  async applyTemplate(templateId) {
    try {
      const result = await electronAPI.templates.applyToCurrentMonth(templateId);
      this.app.showToast(`Applied template: ${result.addedItems} items added`, 'success');
      
      // Refresh current items
      await this.app.loadItems();
    } catch (error) {
      console.error('Failed to apply template:', error);
      this.app.showToast('Failed to apply template', 'error');
    }
  }

  editTemplate(templateId) {
    // TODO: Implement edit template modal
    console.log('Edit template:', templateId);
  }

  async deleteTemplate(templateId) {
    const result = await electronAPI.dialog.showMessage({
      type: 'warning',
      buttons: ['Cancel', 'Delete'],
      defaultId: 0,
      title: 'Delete Template',
      message: 'Are you sure you want to delete this template?',
      detail: 'This action cannot be undone.'
    });

    if (result.response === 1) {
      try {
        await electronAPI.templates.delete(templateId);
        await this.loadTemplates();
        this.app.showToast('Template deleted', 'success');
      } catch (error) {
        console.error('Failed to delete template:', error);
        this.app.showToast('Failed to delete template', 'error');
      }
    }
  }

  compareWithMonth(month, year) {
    // TODO: Implement month comparison
    console.log('Compare with month:', month, year);
  }

  createTemplateFromMonth(month, year) {
    // TODO: Implement create template from month
    console.log('Create template from month:', month, year);
  }

  async copyToCurrentMonth(month, year) {
    try {
      const items = await electronAPI.monthlyLists.getItems(month, year);
      
      if (items.length === 0) {
        this.app.showToast('No items to copy', 'warning');
        return;
      }

      // Add items to current month
      const now = new Date();
      const currentMonth = now.getMonth() + 1;
      const currentYear = now.getFullYear();

      let addedCount = 0;
      for (const item of items) {
        try {
          await electronAPI.monthlyLists.addItemToMonth(item.id, currentMonth, currentYear);
          addedCount++;
        } catch (error) {
          // Skip duplicates
          if (!error.message.includes('already in')) {
            throw error;
          }
        }
      }

      this.app.showToast(`Copied ${addedCount} items to current month`, 'success');
      
      // Refresh current items
      await this.app.loadItems();
    } catch (error) {
      console.error('Failed to copy to current month:', error);
      this.app.showToast('Failed to copy items', 'error');
    }
  }
}

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
  module.exports = HistoryView;
}
