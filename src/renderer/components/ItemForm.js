// Item Form Component for adding and editing grocery items
class ItemManager {
  constructor(app) {
    this.app = app;
  }

  showAddItemModal() {
    const modal = this.createItemModal();
    document.getElementById('modal-container').appendChild(modal);
    
    // Focus on name input
    setTimeout(() => {
      document.getElementById('item-name').focus();
    }, 100);
  }

  showEditItemModal(itemId) {
    const item = this.app.items.find(i => i.id === itemId);
    if (!item) return;

    const modal = this.createItemModal(item);
    document.getElementById('modal-container').appendChild(modal);
    
    // Focus on name input
    setTimeout(() => {
      document.getElementById('item-name').focus();
    }, 100);
  }

  createItemModal(item = null) {
    const isEdit = item !== null;
    const modalDiv = document.createElement('div');
    modalDiv.className = 'modal-overlay';
    modalDiv.innerHTML = `
      <div class="modal-content max-w-lg">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            ${isEdit ? 'Edit Item' : 'Add New Item'}
          </h3>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.closest('.modal-overlay').remove()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <form id="item-form" class="space-y-4">
          <!-- Item Name -->
          <div>
            <label for="item-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Item Name *
            </label>
            <input
              type="text"
              id="item-name"
              name="name"
              class="input-field"
              placeholder="Enter item name"
              value="${isEdit ? electronAPI.utils.sanitizeHTML(item.name) : ''}"
              maxlength="${electronAPI.constants.MAX_ITEM_NAME_LENGTH}"
              required
            />
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Maximum ${electronAPI.constants.MAX_ITEM_NAME_LENGTH} characters
            </p>
          </div>

          <!-- Quantity and Unit -->
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label for="item-quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Quantity *
              </label>
              <input
                type="number"
                id="item-quantity"
                name="quantity"
                class="input-field"
                placeholder="1"
                value="${isEdit ? item.quantity : '1'}"
                min="0.01"
                step="0.01"
                required
              />
            </div>
            <div>
              <label for="item-unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Unit
              </label>
              <select id="item-unit" name="unit" class="input-field">
                ${electronAPI.constants.UNITS.map(unit => 
                  `<option value="${unit}" ${isEdit && item.unit === unit ? 'selected' : ''}>${unit}</option>`
                ).join('')}
              </select>
            </div>
          </div>

          <!-- Brand Name -->
          <div>
            <label for="item-brand" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Brand Name
            </label>
            <input
              type="text"
              id="item-brand"
              name="brand"
              class="input-field"
              placeholder="Enter brand name (optional)"
              value="${isEdit ? electronAPI.utils.sanitizeHTML(item.brand || '') : ''}"
              maxlength="${electronAPI.constants.MAX_BRAND_LENGTH}"
            />
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Maximum ${electronAPI.constants.MAX_BRAND_LENGTH} characters
            </p>
          </div>

          <!-- Category -->
          <div>
            <label for="item-category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Category *
            </label>
            <select id="item-category" name="category_id" class="input-field" required>
              <option value="">Select a category</option>
              ${this.app.categories.map(category => 
                `<option value="${category.id}" ${isEdit && item.category_id === category.id ? 'selected' : ''}>
                  ${electronAPI.utils.sanitizeHTML(category.name)}
                </option>`
              ).join('')}
            </select>
          </div>

          <!-- Price -->
          <div>
            <label for="item-price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Price (৳)
            </label>
            <input
              type="number"
              id="item-price"
              name="price"
              class="input-field"
              placeholder="0.00"
              value="${isEdit ? item.price : ''}"
              min="0"
              step="0.01"
            />
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Optional - for budget tracking
            </p>
          </div>

          <!-- Date Added -->
          <div>
            <label for="item-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Date Added
            </label>
            <input
              type="date"
              id="item-date"
              name="date_added"
              class="input-field"
              value="${isEdit ? item.date_added : new Date().toISOString().split('T')[0]}"
            />
          </div>

          <!-- Remarks -->
          <div>
            <label for="item-remarks" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Remarks
            </label>
            <textarea
              id="item-remarks"
              name="remarks"
              class="input-field"
              rows="3"
              placeholder="Additional notes (optional)"
              maxlength="${electronAPI.constants.MAX_REMARKS_LENGTH}"
            >${isEdit ? electronAPI.utils.sanitizeHTML(item.remarks || '') : ''}</textarea>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Maximum ${electronAPI.constants.MAX_REMARKS_LENGTH} characters
            </p>
          </div>

          <!-- Purchase Status (for edit mode) -->
          ${isEdit ? `
            <div>
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="item-purchased"
                  name="purchased"
                  class="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  ${item.purchased ? 'checked' : ''}
                />
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Mark as purchased
                </span>
              </label>
            </div>
          ` : ''}

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              class="btn-secondary"
              onclick="this.closest('.modal-overlay').remove()"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="btn-primary"
            >
              ${isEdit ? 'Update Item' : 'Add Item'}
            </button>
          </div>
        </form>
      </div>
    `;

    // Add form submit handler
    const form = modalDiv.querySelector('#item-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const formData = new FormData(form);
      const itemData = {
        name: formData.get('name').trim(),
        quantity: parseFloat(formData.get('quantity')),
        unit: formData.get('unit'),
        brand: formData.get('brand').trim(),
        category_id: parseInt(formData.get('category_id')),
        price: parseFloat(formData.get('price')) || 0,
        date_added: formData.get('date_added'),
        remarks: formData.get('remarks').trim(),
        purchased: isEdit ? formData.has('purchased') : false
      };
      
      // Validation
      if (!itemData.name) {
        this.app.showToast('Item name is required', 'error');
        return;
      }

      if (!itemData.category_id) {
        this.app.showToast('Please select a category', 'error');
        return;
      }

      if (itemData.quantity <= 0) {
        this.app.showToast('Quantity must be greater than 0', 'error');
        return;
      }

      try {
        if (isEdit) {
          await this.updateItem(item.id, itemData);
        } else {
          await this.createItem(itemData);
        }
        
        modalDiv.remove();
      } catch (error) {
        console.error('Item operation failed:', error);
        this.app.showToast(error.message || 'Operation failed', 'error');
      }
    });

    // Close modal on escape key
    modalDiv.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        modalDiv.remove();
      }
    });

    // Close modal on backdrop click
    modalDiv.addEventListener('click', (e) => {
      if (e.target === modalDiv) {
        modalDiv.remove();
      }
    });

    // Add character count for text fields
    this.addCharacterCounters(modalDiv);

    return modalDiv;
  }

  addCharacterCounters(modal) {
    const textFields = [
      { id: 'item-name', max: electronAPI.constants.MAX_ITEM_NAME_LENGTH },
      { id: 'item-brand', max: electronAPI.constants.MAX_BRAND_LENGTH },
      { id: 'item-remarks', max: electronAPI.constants.MAX_REMARKS_LENGTH }
    ];

    textFields.forEach(({ id, max }) => {
      const field = modal.querySelector(`#${id}`);
      const helpText = field.nextElementSibling;
      
      if (field && helpText) {
        const updateCounter = () => {
          const remaining = max - field.value.length;
          helpText.textContent = `${remaining} characters remaining`;
          
          if (remaining < 10) {
            helpText.classList.add('text-red-500');
            helpText.classList.remove('text-gray-500', 'dark:text-gray-400');
          } else {
            helpText.classList.remove('text-red-500');
            helpText.classList.add('text-gray-500', 'dark:text-gray-400');
          }
        };

        field.addEventListener('input', updateCounter);
        updateCounter(); // Initial update
      }
    });
  }

  async createItem(data) {
    try {
      const newItem = await electronAPI.items.create(data);
      
      // Add to local state
      this.app.items.push(newItem);
      
      // Re-render items and update statistics
      this.app.filterAndRenderItems();
      await this.app.updateStatistics();
      
      this.app.showToast('Item added successfully', 'success');
      
      return newItem;
    } catch (error) {
      console.error('Failed to create item:', error);
      throw error;
    }
  }

  async updateItem(itemId, data) {
    try {
      const updatedItem = await electronAPI.items.update(itemId, data);
      
      // Update local state
      const index = this.app.items.findIndex(i => i.id === itemId);
      if (index !== -1) {
        this.app.items[index] = updatedItem;
      }
      
      // Re-render items and update statistics
      this.app.filterAndRenderItems();
      await this.app.updateStatistics();
      
      this.app.showToast('Item updated successfully', 'success');
      
      return updatedItem;
    } catch (error) {
      console.error('Failed to update item:', error);
      throw error;
    }
  }

  async deleteItem(itemId) {
    const item = this.app.items.find(i => i.id === itemId);
    if (!item) return;

    const result = await electronAPI.dialog.showMessage({
      type: 'warning',
      buttons: ['Cancel', 'Delete'],
      defaultId: 0,
      title: 'Delete Item',
      message: `Are you sure you want to delete "${item.name}"?`,
      detail: 'This action cannot be undone.'
    });

    if (result.response === 1) {
      try {
        await electronAPI.items.delete(itemId);
        
        // Remove from local state
        this.app.items = this.app.items.filter(i => i.id !== itemId);
        
        // Remove from selection if selected
        this.app.selectedItems.delete(itemId);
        
        // Re-render and update statistics
        this.app.filterAndRenderItems();
        await this.app.updateStatistics();
        
        this.app.showToast('Item deleted successfully', 'success');
      } catch (error) {
        console.error('Failed to delete item:', error);
        this.app.showToast('Failed to delete item', 'error');
      }
    }
  }

  async duplicateItem(itemId) {
    const item = this.app.items.find(i => i.id === itemId);
    if (!item) return;

    const duplicateData = {
      name: `${item.name} (Copy)`,
      quantity: item.quantity,
      unit: item.unit,
      brand: item.brand,
      category_id: item.category_id,
      price: item.price,
      remarks: item.remarks,
      purchased: false,
      date_added: new Date().toISOString().split('T')[0]
    };

    try {
      await this.createItem(duplicateData);
      this.app.showToast('Item duplicated successfully', 'success');
    } catch (error) {
      console.error('Failed to duplicate item:', error);
      this.app.showToast('Failed to duplicate item', 'error');
    }
  }
}

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ItemManager;
}
