// Search and Filter Component for advanced filtering capabilities
class SearchFilter {
  constructor(app) {
    this.app = app;
    this.advancedFilters = {
      dateFrom: null,
      dateTo: null,
      priceMin: null,
      priceMax: null,
      categories: [],
      units: [],
      hasBrand: null,
      hasRemarks: null
    };
    this.isAdvancedMode = false;
  }

  showAdvancedFilterModal() {
    const modal = this.createAdvancedFilterModal();
    document.getElementById('modal-container').appendChild(modal);
  }

  createAdvancedFilterModal() {
    const modalDiv = document.createElement('div');
    modalDiv.className = 'modal-overlay';
    modalDiv.innerHTML = `
      <div class="modal-content max-w-2xl">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Advanced Filters
          </h3>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.closest('.modal-overlay').remove()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <form id="advanced-filter-form" class="space-y-6">
          <!-- Date Range -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Date Range</h4>
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label for="date-from" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">From</label>
                <input
                  type="date"
                  id="date-from"
                  name="dateFrom"
                  class="input-field text-sm"
                  value="${this.advancedFilters.dateFrom || ''}"
                />
              </div>
              <div>
                <label for="date-to" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">To</label>
                <input
                  type="date"
                  id="date-to"
                  name="dateTo"
                  class="input-field text-sm"
                  value="${this.advancedFilters.dateTo || ''}"
                />
              </div>
            </div>
          </div>

          <!-- Price Range -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Price Range (৳)</h4>
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label for="price-min" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Minimum</label>
                <input
                  type="number"
                  id="price-min"
                  name="priceMin"
                  class="input-field text-sm"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  value="${this.advancedFilters.priceMin || ''}"
                />
              </div>
              <div>
                <label for="price-max" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Maximum</label>
                <input
                  type="number"
                  id="price-max"
                  name="priceMax"
                  class="input-field text-sm"
                  placeholder="1000.00"
                  min="0"
                  step="0.01"
                  value="${this.advancedFilters.priceMax || ''}"
                />
              </div>
            </div>
          </div>

          <!-- Categories -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Categories</h4>
            <div class="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
              ${this.app.categories.map(category => `
                <label class="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    name="categories"
                    value="${category.id}"
                    class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    ${this.advancedFilters.categories.includes(category.id) ? 'checked' : ''}
                  />
                  <span class="text-sm text-gray-700 dark:text-gray-300">${electronAPI.utils.sanitizeHTML(category.name)}</span>
                </label>
              `).join('')}
            </div>
          </div>

          <!-- Units -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Units</h4>
            <div class="grid grid-cols-3 gap-2">
              ${electronAPI.constants.UNITS.map(unit => `
                <label class="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    name="units"
                    value="${unit}"
                    class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    ${this.advancedFilters.units.includes(unit) ? 'checked' : ''}
                  />
                  <span class="text-sm text-gray-700 dark:text-gray-300">${unit}</span>
                </label>
              `).join('')}
            </div>
          </div>

          <!-- Additional Filters -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Additional Filters</h4>
            <div class="space-y-2">
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name="hasBrand"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  ${this.advancedFilters.hasBrand === true ? 'checked' : ''}
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">Has brand name</span>
              </label>
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name="hasRemarks"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  ${this.advancedFilters.hasRemarks === true ? 'checked' : ''}
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">Has remarks</span>
              </label>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              id="clear-filters-btn"
              class="btn-secondary text-sm"
            >
              Clear All Filters
            </button>
            <div class="space-x-3">
              <button
                type="button"
                class="btn-secondary"
                onclick="this.closest('.modal-overlay').remove()"
              >
                Cancel
              </button>
              <button
                type="submit"
                class="btn-primary"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </form>
      </div>
    `;

    // Add form submit handler
    const form = modalDiv.querySelector('#advanced-filter-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.applyAdvancedFilters(form);
      modalDiv.remove();
    });

    // Clear filters handler
    modalDiv.querySelector('#clear-filters-btn').addEventListener('click', () => {
      this.clearAdvancedFilters();
      modalDiv.remove();
    });

    // Close modal on escape key
    modalDiv.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        modalDiv.remove();
      }
    });

    // Close modal on backdrop click
    modalDiv.addEventListener('click', (e) => {
      if (e.target === modalDiv) {
        modalDiv.remove();
      }
    });

    return modalDiv;
  }

  applyAdvancedFilters(form) {
    const formData = new FormData(form);
    
    // Update advanced filters
    this.advancedFilters = {
      dateFrom: formData.get('dateFrom') || null,
      dateTo: formData.get('dateTo') || null,
      priceMin: parseFloat(formData.get('priceMin')) || null,
      priceMax: parseFloat(formData.get('priceMax')) || null,
      categories: formData.getAll('categories').map(id => parseInt(id)),
      units: formData.getAll('units'),
      hasBrand: formData.has('hasBrand'),
      hasRemarks: formData.has('hasRemarks')
    };

    this.isAdvancedMode = this.hasActiveFilters();
    this.updateFilterIndicator();
    this.app.filterAndRenderItems();
    
    this.app.showToast('Advanced filters applied', 'success');
  }

  clearAdvancedFilters() {
    this.advancedFilters = {
      dateFrom: null,
      dateTo: null,
      priceMin: null,
      priceMax: null,
      categories: [],
      units: [],
      hasBrand: null,
      hasRemarks: null
    };
    
    this.isAdvancedMode = false;
    this.updateFilterIndicator();
    this.app.filterAndRenderItems();
    
    this.app.showToast('All filters cleared', 'success');
  }

  hasActiveFilters() {
    return this.advancedFilters.dateFrom ||
           this.advancedFilters.dateTo ||
           this.advancedFilters.priceMin !== null ||
           this.advancedFilters.priceMax !== null ||
           this.advancedFilters.categories.length > 0 ||
           this.advancedFilters.units.length > 0 ||
           this.advancedFilters.hasBrand ||
           this.advancedFilters.hasRemarks;
  }

  updateFilterIndicator() {
    // Add visual indicator for active advanced filters
    const searchContainer = document.querySelector('.relative');
    let indicator = searchContainer.querySelector('.filter-indicator');
    
    if (this.isAdvancedMode) {
      if (!indicator) {
        indicator = document.createElement('div');
        indicator.className = 'filter-indicator absolute top-2 right-2 w-2 h-2 bg-primary-600 rounded-full';
        searchContainer.appendChild(indicator);
      }
    } else {
      if (indicator) {
        indicator.remove();
      }
    }
  }

  filterItems(items) {
    if (!this.isAdvancedMode) {
      return items;
    }

    return items.filter(item => {
      // Date range filter
      if (this.advancedFilters.dateFrom && item.date_added < this.advancedFilters.dateFrom) {
        return false;
      }
      if (this.advancedFilters.dateTo && item.date_added > this.advancedFilters.dateTo) {
        return false;
      }

      // Price range filter
      if (this.advancedFilters.priceMin !== null && item.price < this.advancedFilters.priceMin) {
        return false;
      }
      if (this.advancedFilters.priceMax !== null && item.price > this.advancedFilters.priceMax) {
        return false;
      }

      // Category filter
      if (this.advancedFilters.categories.length > 0 && !this.advancedFilters.categories.includes(item.category_id)) {
        return false;
      }

      // Unit filter
      if (this.advancedFilters.units.length > 0 && !this.advancedFilters.units.includes(item.unit)) {
        return false;
      }

      // Brand filter
      if (this.advancedFilters.hasBrand && (!item.brand || item.brand.trim() === '')) {
        return false;
      }

      // Remarks filter
      if (this.advancedFilters.hasRemarks && (!item.remarks || item.remarks.trim() === '')) {
        return false;
      }

      return true;
    });
  }

  // Quick filter methods
  filterByCategory(categoryId) {
    this.advancedFilters.categories = [categoryId];
    this.isAdvancedMode = true;
    this.updateFilterIndicator();
    this.app.filterAndRenderItems();
  }

  filterByUnit(unit) {
    this.advancedFilters.units = [unit];
    this.isAdvancedMode = true;
    this.updateFilterIndicator();
    this.app.filterAndRenderItems();
  }

  filterByPriceRange(min, max) {
    this.advancedFilters.priceMin = min;
    this.advancedFilters.priceMax = max;
    this.isAdvancedMode = true;
    this.updateFilterIndicator();
    this.app.filterAndRenderItems();
  }

  filterByDateRange(from, to) {
    this.advancedFilters.dateFrom = from;
    this.advancedFilters.dateTo = to;
    this.isAdvancedMode = true;
    this.updateFilterIndicator();
    this.app.filterAndRenderItems();
  }

  // Search suggestions
  getSearchSuggestions(query) {
    if (!query || query.length < 2) {
      return [];
    }

    const suggestions = new Set();
    const lowerQuery = query.toLowerCase();

    this.app.items.forEach(item => {
      // Item names
      if (item.name.toLowerCase().includes(lowerQuery)) {
        suggestions.add(item.name);
      }

      // Brand names
      if (item.brand && item.brand.toLowerCase().includes(lowerQuery)) {
        suggestions.add(item.brand);
      }

      // Category names
      if (item.category_name && item.category_name.toLowerCase().includes(lowerQuery)) {
        suggestions.add(item.category_name);
      }
    });

    return Array.from(suggestions).slice(0, 5);
  }

  showSearchSuggestions(query) {
    const suggestions = this.getSearchSuggestions(query);
    const searchInput = document.getElementById('search-input');
    
    // Remove existing suggestions
    const existingSuggestions = document.querySelector('.search-suggestions');
    if (existingSuggestions) {
      existingSuggestions.remove();
    }

    if (suggestions.length === 0) {
      return;
    }

    // Create suggestions dropdown
    const suggestionsDiv = document.createElement('div');
    suggestionsDiv.className = 'search-suggestions absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-b-lg shadow-lg z-10 max-h-40 overflow-y-auto';
    
    suggestions.forEach(suggestion => {
      const suggestionItem = document.createElement('div');
      suggestionItem.className = 'px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-700 dark:text-gray-300';
      suggestionItem.textContent = suggestion;
      
      suggestionItem.addEventListener('click', () => {
        searchInput.value = suggestion;
        this.app.searchQuery = suggestion;
        this.app.filterAndRenderItems();
        suggestionsDiv.remove();
      });
      
      suggestionsDiv.appendChild(suggestionItem);
    });

    // Position suggestions
    const searchContainer = searchInput.parentElement;
    searchContainer.style.position = 'relative';
    searchContainer.appendChild(suggestionsDiv);

    // Remove suggestions when clicking outside
    setTimeout(() => {
      document.addEventListener('click', function removeSuggestions(e) {
        if (!searchContainer.contains(e.target)) {
          suggestionsDiv.remove();
          document.removeEventListener('click', removeSuggestions);
        }
      });
    }, 100);
  }

  // Saved searches
  saveSearch(name, query, filters) {
    const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
    
    const search = {
      id: Date.now(),
      name,
      query,
      filters,
      createdAt: new Date().toISOString()
    };
    
    savedSearches.push(search);
    localStorage.setItem('savedSearches', JSON.stringify(savedSearches));
    
    return search;
  }

  getSavedSearches() {
    return JSON.parse(localStorage.getItem('savedSearches') || '[]');
  }

  applySavedSearch(searchId) {
    const savedSearches = this.getSavedSearches();
    const search = savedSearches.find(s => s.id === searchId);
    
    if (search) {
      this.app.searchQuery = search.query;
      this.advancedFilters = search.filters;
      this.isAdvancedMode = this.hasActiveFilters();
      
      document.getElementById('search-input').value = search.query;
      this.updateFilterIndicator();
      this.app.filterAndRenderItems();
    }
  }

  deleteSavedSearch(searchId) {
    const savedSearches = this.getSavedSearches();
    const filtered = savedSearches.filter(s => s.id !== searchId);
    localStorage.setItem('savedSearches', JSON.stringify(filtered));
  }
}

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SearchFilter;
}
