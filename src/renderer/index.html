<!DOCTYPE html>
<html lang="en" class="h-full">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Security CSP -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';"
    />
    <title>Shodaipati - Monthly Grocery List Manager</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- TailwindCSS -->
    <link rel="stylesheet" href="styles/main.css">

    <!-- Icons (Heroicons) -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>
  </head>
  <body class="h-full bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-200">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Loading Shodaipati...</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-2">Setting up your grocery list manager</p>
      </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="hidden h-full flex flex-col">
      <!-- Header -->
      <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
          <!-- Logo and Title -->
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">🛒</span>
            </div>
            <div>
              <h1 class="text-xl font-bold text-gray-900 dark:text-gray-100">Shodaipati</h1>
              <p class="text-sm text-gray-600 dark:text-gray-400">Monthly Grocery Manager</p>
            </div>
          </div>

          <!-- Header Actions -->
          <div class="flex items-center space-x-4">
            <!-- Statistics -->
            <div id="header-stats" class="hidden md:flex items-center space-x-6 text-sm">
              <div class="text-center">
                <div class="font-semibold text-gray-900 dark:text-gray-100" id="total-items">0</div>
                <div class="text-gray-600 dark:text-gray-400">Total Items</div>
              </div>
              <div class="text-center">
                <div class="font-semibold text-green-600 dark:text-green-400" id="purchased-items">0</div>
                <div class="text-gray-600 dark:text-gray-400">Purchased</div>
              </div>
              <div class="text-center">
                <div class="font-semibold text-primary-600 dark:text-primary-400" id="total-budget">৳0</div>
                <div class="text-gray-600 dark:text-gray-400">Budget</div>
              </div>
            </div>

            <!-- Theme Toggle -->
            <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200" title="Toggle theme">
              <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
              </svg>
            </button>

            <!-- Menu Button -->
            <button id="menu-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200" title="Menu">
              <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="flex-1 flex overflow-hidden">
        <!-- Sidebar -->
        <aside id="sidebar" class="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
          <!-- Search and Filters -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <!-- Search Bar -->
            <div class="relative mb-4">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
              <input
                type="text"
                id="search-input"
                class="search-bar"
                placeholder="Search items, brands, or categories..."
                autocomplete="off"
              />
              <button id="clear-search" class="absolute inset-y-0 right-0 pr-3 flex items-center hidden">
                <svg class="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- Filter Buttons -->
            <div class="flex flex-wrap gap-2 mb-4">
              <button id="filter-all" class="btn-secondary text-xs py-1 px-3 active">All</button>
              <button id="filter-pending" class="btn-secondary text-xs py-1 px-3">Pending</button>
              <button id="filter-purchased" class="btn-secondary text-xs py-1 px-3">Purchased</button>
              <button id="advanced-filter-btn" class="btn-secondary text-xs py-1 px-3" title="Advanced Filters">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"></path>
                </svg>
              </button>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-2">
              <button id="add-item-btn" class="btn-primary flex-1 text-sm py-2">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Item
              </button>
              <button id="add-category-btn" class="btn-secondary text-sm py-2 px-3" title="Add Category">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- Categories and Items List -->
          <div class="flex-1 overflow-y-auto scrollbar-thin">
            <div id="categories-container" class="p-4">
              <!-- Categories will be dynamically loaded here -->
            </div>
          </div>

          <!-- Bulk Actions -->
          <div id="bulk-actions" class="hidden p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                <span id="selected-count">0</span> items selected
              </span>
              <button id="clear-selection" class="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                Clear
              </button>
            </div>
            <div class="flex gap-2">
              <button id="mark-purchased-btn" class="btn-primary text-xs py-1 px-3 flex-1">Mark Purchased</button>
              <button id="mark-pending-btn" class="btn-secondary text-xs py-1 px-3 flex-1">Mark Pending</button>
              <button id="delete-selected-btn" class="btn-danger text-xs py-1 px-3">Delete</button>
            </div>
          </div>
        </aside>

        <!-- Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
          <!-- Content Header -->
          <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Grocery List</h2>
                <p class="text-sm text-gray-600 dark:text-gray-400" id="current-month">
                  <!-- Current month will be set by JavaScript -->
                </p>
              </div>

              <div class="flex items-center space-x-3">
                <!-- View Toggle -->
                <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                  <button id="view-list" class="px-3 py-1 text-sm rounded-md bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm">List</button>
                  <button id="view-grid" class="px-3 py-1 text-sm rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">Grid</button>
                </div>

                <!-- Export Button -->
                <button id="export-btn" class="btn-secondary text-sm py-2 px-4">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Export
                </button>
              </div>
            </div>
          </div>

          <!-- Main Content Area -->
          <div class="flex-1 overflow-y-auto scrollbar-thin bg-gray-50 dark:bg-gray-900">
            <div id="main-content" class="p-6">
              <!-- Content will be dynamically loaded here -->
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Modals and Overlays will be inserted here -->
    <div id="modal-container"></div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    <script src="components/SearchFilter.js"></script>
    <script src="components/CategoryManager.js"></script>
    <script src="components/ItemForm.js"></script>
    <script src="components/ExportManager.js"></script>
    <script src="renderer.js"></script>
  </body>
</html>