// Main renderer process for Shodaipati grocery list application
// Import performance optimizations
const performanceOptimizer = require('../utils/performance');

class ShodaipatiApp {
  constructor() {
    this.categories = [];
    this.items = [];
    this.selectedItems = new Set();
    this.currentFilter = 'all';
    this.searchQuery = '';
    this.currentView = 'list';
    this.isDarkMode = false;

    // Initialize components
    this.searchFilter = null;
    this.categoryManager = null;
    this.itemManager = null;
    this.exportManager = null;

    // Initialize the application
    this.init();
  }

  async init() {
    try {
      // Show loading screen
      this.showLoading();
      
      // Initialize theme
      this.initializeTheme();
      
      // Initialize components
      this.initializeComponents();

      // Setup event listeners
      this.setupEventListeners();

      // Load initial data
      await this.loadCategories();
      await this.loadItems();
      
      // Update statistics
      await this.updateStatistics();
      
      // Set current month
      this.setCurrentMonth();
      
      // Hide loading screen and show app
      this.hideLoading();
      
      // Show welcome message
      this.showToast('Welcome to <PERSON><PERSON><PERSON>pa<PERSON>!', 'success');
      
    } catch (error) {
      console.error('Failed to initialize app:', error);
      this.showToast('Failed to initialize application', 'error');
      this.hideLoading();
    }
  }

  showLoading() {
    document.getElementById('loading-screen').classList.remove('hidden');
    document.getElementById('app').classList.add('hidden');
  }

  hideLoading() {
    document.getElementById('loading-screen').classList.add('hidden');
    document.getElementById('app').classList.remove('hidden');
  }

  initializeTheme() {
    // Check for saved theme preference or default to system preference
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    this.isDarkMode = savedTheme === 'dark' || (!savedTheme && systemPrefersDark);
    this.applyTheme();
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        this.isDarkMode = e.matches;
        this.applyTheme();
      }
    });
  }

  initializeComponents() {
    // Initialize search filter component
    this.searchFilter = new SearchFilter(this);
  }

  applyTheme() {
    if (this.isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Update theme toggle icon
    const themeToggle = document.getElementById('theme-toggle');
    const icon = themeToggle.querySelector('svg');

    if (this.isDarkMode) {
      icon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>`;
    } else {
      icon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>`;
    }
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
    localStorage.setItem('theme', this.isDarkMode ? 'dark' : 'light');
    this.applyTheme();
  }

  setupEventListeners() {
    // Theme toggle
    document.getElementById('theme-toggle').addEventListener('click', () => {
      this.toggleTheme();
    });

    // Search functionality
    const searchInput = document.getElementById('search-input');
    const clearSearch = document.getElementById('clear-search');
    
    searchInput.addEventListener('input', performanceOptimizer.debounce((e) => {
      this.searchQuery = e.target.value.trim();
      this.filterAndRenderItems();

      if (this.searchQuery) {
        clearSearch.classList.remove('hidden');
        // Show search suggestions
        if (this.searchFilter) {
          this.searchFilter.showSearchSuggestions(this.searchQuery);
        }
      } else {
        clearSearch.classList.add('hidden');
      }
    }, 300));

    clearSearch.addEventListener('click', () => {
      searchInput.value = '';
      this.searchQuery = '';
      clearSearch.classList.add('hidden');
      this.filterAndRenderItems();
    });

    // Filter buttons
    document.getElementById('filter-all').addEventListener('click', () => {
      this.setFilter('all');
    });
    
    document.getElementById('filter-pending').addEventListener('click', () => {
      this.setFilter('pending');
    });
    
    document.getElementById('filter-purchased').addEventListener('click', () => {
      this.setFilter('purchased');
    });

    // Advanced filter button
    document.getElementById('advanced-filter-btn').addEventListener('click', () => {
      if (!this.searchFilter) {
        this.searchFilter = new SearchFilter(this);
      }
      this.searchFilter.showAdvancedFilterModal();
    });

    // Add item button
    document.getElementById('add-item-btn').addEventListener('click', () => {
      this.showAddItemModal();
    });

    // Add category button
    document.getElementById('add-category-btn').addEventListener('click', () => {
      this.showAddCategoryModal();
    });

    // View toggle
    document.getElementById('view-list').addEventListener('click', () => {
      this.setView('list');
    });
    
    document.getElementById('view-grid').addEventListener('click', () => {
      this.setView('grid');
    });

    // Export button
    document.getElementById('export-btn').addEventListener('click', () => {
      this.showExportModal();
    });

    // Bulk actions
    document.getElementById('clear-selection').addEventListener('click', () => {
      this.clearSelection();
    });

    document.getElementById('mark-purchased-btn').addEventListener('click', () => {
      this.markSelectedPurchased(true);
    });

    document.getElementById('mark-pending-btn').addEventListener('click', () => {
      this.markSelectedPurchased(false);
    });

    document.getElementById('delete-selected-btn').addEventListener('click', () => {
      this.deleteSelected();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + N: Add new item
      if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        this.showAddItemModal();
      }
      
      // Ctrl/Cmd + F: Focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        searchInput.focus();
      }
      
      // Escape: Clear search or close modals
      if (e.key === 'Escape') {
        if (this.searchQuery) {
          searchInput.value = '';
          this.searchQuery = '';
          clearSearch.classList.add('hidden');
          this.filterAndRenderItems();
        }
      }
    });
  }

  async loadCategories() {
    try {
      this.categories = await electronAPI.categories.getAll();
      this.renderCategories();
    } catch (error) {
      console.error('Failed to load categories:', error);
      this.showToast('Failed to load categories', 'error');
    }
  }

  async loadItems() {
    try {
      this.items = await electronAPI.items.getAll();
      this.filterAndRenderItems();
    } catch (error) {
      console.error('Failed to load items:', error);
      this.showToast('Failed to load items', 'error');
    }
  }

  renderCategories() {
    const container = document.getElementById('categories-container');
    container.innerHTML = '';

    if (this.categories.length === 0) {
      container.innerHTML = `
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>No categories found</p>
          <button class="btn-primary mt-2" onclick="app.showAddCategoryModal()">Add Category</button>
        </div>
      `;
      return;
    }

    this.categories.forEach(category => {
      const categoryItems = this.getFilteredItemsForCategory(category.id);
      const categoryElement = this.createCategoryElement(category, categoryItems);
      container.appendChild(categoryElement);
    });
  }

  getFilteredItemsForCategory(categoryId) {
    let filteredItems = this.items.filter(item => {
      if (item.category_id !== categoryId) return false;

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        const matchesName = item.name.toLowerCase().includes(query);
        const matchesBrand = item.brand && item.brand.toLowerCase().includes(query);
        const matchesCategory = item.category_name && item.category_name.toLowerCase().includes(query);

        if (!matchesName && !matchesBrand && !matchesCategory) {
          return false;
        }
      }

      // Apply status filter
      if (this.currentFilter === 'pending' && item.purchased) return false;
      if (this.currentFilter === 'purchased' && !item.purchased) return false;

      return true;
    });

    // Apply advanced filters if search filter component is available
    if (this.searchFilter) {
      filteredItems = this.searchFilter.filterItems(filteredItems);
    }

    return filteredItems;
  }

  createCategoryElement(category, items) {
    const categoryDiv = document.createElement('div');
    categoryDiv.className = 'category-section mb-4';
    categoryDiv.innerHTML = `
      <div class="category-header flex items-center justify-between" onclick="app.toggleCategory(${category.id})">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 transform transition-transform category-chevron" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
          <h3 class="font-medium text-gray-900 dark:text-gray-100">${electronAPI.utils.sanitizeHTML(category.name)}</h3>
          <span class="text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full">
            ${items.length}
          </span>
        </div>
        <div class="flex items-center space-x-1">
          <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" onclick="event.stopPropagation(); app.editCategory(${category.id})" title="Edit category">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
          </button>
          <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-red-600" onclick="event.stopPropagation(); app.deleteCategory(${category.id})" title="Delete category">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
      <div class="category-items" id="category-${category.id}-items" style="display: none;">
        ${this.renderCategoryItems(items)}
      </div>
    `;
    
    return categoryDiv;
  }

  renderCategoryItems(items) {
    if (items.length === 0) {
      return `
        <div class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
          No items in this category
        </div>
      `;
    }

    return items.map(item => this.createItemElement(item)).join('');
  }

  createItemElement(item) {
    const isSelected = this.selectedItems.has(item.id);
    const purchasedClass = item.purchased ? 'purchased-item' : '';
    
    return `
      <div class="item-row ${purchasedClass}" data-item-id="${item.id}">
        <div class="flex items-center space-x-3 flex-1">
          <input type="checkbox" 
                 class="item-checkbox rounded border-gray-300 text-primary-600 focus:ring-primary-500" 
                 ${isSelected ? 'checked' : ''}
                 onchange="app.toggleItemSelection(${item.id})">
          
          <input type="checkbox" 
                 class="purchased-checkbox rounded border-gray-300 text-green-600 focus:ring-green-500" 
                 ${item.purchased ? 'checked' : ''}
                 onchange="app.toggleItemPurchased(${item.id})"
                 title="Mark as purchased">
          
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-2">
              <h4 class="font-medium text-gray-900 dark:text-gray-100 truncate">${electronAPI.utils.sanitizeHTML(item.name)}</h4>
              ${item.brand ? `<span class="text-xs text-gray-500 dark:text-gray-400 truncate">${electronAPI.utils.sanitizeHTML(item.brand)}</span>` : ''}
            </div>
            <div class="flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-400">
              <span>${item.quantity} ${item.unit}</span>
              ${item.price > 0 ? `<span>•</span><span>${electronAPI.utils.formatCurrency(item.price)}</span>` : ''}
              ${item.remarks ? `<span>•</span><span class="truncate">${electronAPI.utils.sanitizeHTML(item.remarks)}</span>` : ''}
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-1">
          <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" onclick="app.editItem(${item.id})" title="Edit item">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
          </button>
          <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-red-600" onclick="app.deleteItem(${item.id})" title="Delete item">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
  }

  filterAndRenderItems() {
    this.renderCategories();
    this.updateBulkActions();
  }

  setFilter(filter) {
    this.currentFilter = filter;
    
    // Update filter button states
    document.querySelectorAll('[id^="filter-"]').forEach(btn => {
      btn.classList.remove('bg-primary-600', 'text-white');
      btn.classList.add('btn-secondary');
    });
    
    document.getElementById(`filter-${filter}`).classList.remove('btn-secondary');
    document.getElementById(`filter-${filter}`).classList.add('bg-primary-600', 'text-white');
    
    this.filterAndRenderItems();
  }

  setView(view) {
    this.currentView = view;
    
    // Update view button states
    document.getElementById('view-list').classList.remove('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-gray-100', 'shadow-sm');
    document.getElementById('view-grid').classList.remove('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-gray-100', 'shadow-sm');
    
    document.getElementById('view-list').classList.add('text-gray-600', 'dark:text-gray-400');
    document.getElementById('view-grid').classList.add('text-gray-600', 'dark:text-gray-400');
    
    if (view === 'list') {
      document.getElementById('view-list').classList.remove('text-gray-600', 'dark:text-gray-400');
      document.getElementById('view-list').classList.add('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-gray-100', 'shadow-sm');
    } else {
      document.getElementById('view-grid').classList.remove('text-gray-600', 'dark:text-gray-400');
      document.getElementById('view-grid').classList.add('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-gray-100', 'shadow-sm');
    }
    
    // TODO: Implement grid view rendering
    this.filterAndRenderItems();
  }

  toggleCategory(categoryId) {
    const itemsContainer = document.getElementById(`category-${categoryId}-items`);
    const chevron = itemsContainer.previousElementSibling.querySelector('.category-chevron');
    
    if (itemsContainer.style.display === 'none') {
      itemsContainer.style.display = 'block';
      chevron.style.transform = 'rotate(90deg)';
    } else {
      itemsContainer.style.display = 'none';
      chevron.style.transform = 'rotate(0deg)';
    }
  }

  toggleItemSelection(itemId) {
    if (this.selectedItems.has(itemId)) {
      this.selectedItems.delete(itemId);
    } else {
      this.selectedItems.add(itemId);
    }
    
    this.updateBulkActions();
  }

  clearSelection() {
    this.selectedItems.clear();
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
      checkbox.checked = false;
    });
    this.updateBulkActions();
  }

  updateBulkActions() {
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    if (this.selectedItems.size > 0) {
      bulkActions.classList.remove('hidden');
      selectedCount.textContent = this.selectedItems.size;
    } else {
      bulkActions.classList.add('hidden');
    }
  }

  async toggleItemPurchased(itemId) {
    try {
      const item = this.items.find(i => i.id === itemId);
      if (!item) return;
      
      const newPurchasedState = !item.purchased;
      await electronAPI.items.markPurchased(itemId, newPurchasedState);
      
      // Update local state
      item.purchased = newPurchasedState;
      
      // Re-render items and update statistics
      this.filterAndRenderItems();
      await this.updateStatistics();
      
      this.showToast(
        `Item ${newPurchasedState ? 'marked as purchased' : 'marked as pending'}`,
        'success'
      );
    } catch (error) {
      console.error('Failed to toggle item purchased state:', error);
      this.showToast('Failed to update item', 'error');
    }
  }

  async markSelectedPurchased(purchased) {
    if (this.selectedItems.size === 0) return;
    
    try {
      const itemIds = Array.from(this.selectedItems);
      await electronAPI.items.markMultiplePurchased(itemIds, purchased);
      
      // Update local state
      itemIds.forEach(id => {
        const item = this.items.find(i => i.id === id);
        if (item) item.purchased = purchased;
      });
      
      // Clear selection and re-render
      this.clearSelection();
      this.filterAndRenderItems();
      await this.updateStatistics();
      
      this.showToast(
        `${itemIds.length} items ${purchased ? 'marked as purchased' : 'marked as pending'}`,
        'success'
      );
    } catch (error) {
      console.error('Failed to update items:', error);
      this.showToast('Failed to update items', 'error');
    }
  }

  async deleteSelected() {
    if (this.selectedItems.size === 0) return;
    
    const result = await electronAPI.dialog.showMessage({
      type: 'warning',
      buttons: ['Cancel', 'Delete'],
      defaultId: 0,
      title: 'Delete Items',
      message: `Are you sure you want to delete ${this.selectedItems.size} selected items?`,
      detail: 'This action cannot be undone.'
    });
    
    if (result.response === 1) {
      try {
        const itemIds = Array.from(this.selectedItems);
        await electronAPI.items.deleteMultiple(itemIds);
        
        // Remove from local state
        this.items = this.items.filter(item => !itemIds.includes(item.id));
        
        // Clear selection and re-render
        this.clearSelection();
        this.filterAndRenderItems();
        await this.updateStatistics();
        
        this.showToast(`${itemIds.length} items deleted`, 'success');
      } catch (error) {
        console.error('Failed to delete items:', error);
        this.showToast('Failed to delete items', 'error');
      }
    }
  }

  async updateStatistics() {
    try {
      const stats = await electronAPI.items.getStatistics();
      
      document.getElementById('total-items').textContent = stats.total;
      document.getElementById('purchased-items').textContent = stats.purchased;
      document.getElementById('total-budget').textContent = electronAPI.utils.formatCurrency(stats.totalValue);
    } catch (error) {
      console.error('Failed to update statistics:', error);
    }
  }

  setCurrentMonth() {
    const now = new Date();
    const monthName = now.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    document.getElementById('current-month').textContent = monthName;
  }

  showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `animate-slide-up bg-white dark:bg-gray-800 border-l-4 p-4 rounded-lg shadow-lg max-w-sm ${
      type === 'success' ? 'border-green-500' :
      type === 'error' ? 'border-red-500' :
      type === 'warning' ? 'border-yellow-500' :
      'border-blue-500'
    }`;
    
    toast.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          ${type === 'success' ? 
            '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
            type === 'error' ?
            '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' :
            '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
          }
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900 dark:text-gray-100">${electronAPI.utils.sanitizeHTML(message)}</p>
        </div>
        <div class="ml-auto pl-3">
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.parentElement.parentElement.parentElement.remove()">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
    
    document.getElementById('toast-container').appendChild(toast);
    
    // Auto remove after duration
    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove();
      }
    }, duration);
  }

  // Modal methods
  showAddItemModal() {
    if (!this.itemManager) {
      this.itemManager = new ItemManager(this);
    }
    this.itemManager.showAddItemModal();
  }

  showAddCategoryModal() {
    if (!this.categoryManager) {
      this.categoryManager = new CategoryManager(this);
    }
    this.categoryManager.showAddCategoryModal();
  }

  showExportModal() {
    if (!this.exportManager) {
      this.exportManager = new ExportManager(this);
    }
    this.exportManager.showExportModal();
  }

  editItem(itemId) {
    if (!this.itemManager) {
      this.itemManager = new ItemManager(this);
    }
    this.itemManager.showEditItemModal(itemId);
  }

  editCategory(categoryId) {
    if (!this.categoryManager) {
      this.categoryManager = new CategoryManager(this);
    }
    this.categoryManager.showEditCategoryModal(categoryId);
  }

  async deleteItem(itemId) {
    const result = await electronAPI.dialog.showMessage({
      type: 'warning',
      buttons: ['Cancel', 'Delete'],
      defaultId: 0,
      title: 'Delete Item',
      message: 'Are you sure you want to delete this item?',
      detail: 'This action cannot be undone.'
    });
    
    if (result.response === 1) {
      try {
        await electronAPI.items.delete(itemId);
        
        // Remove from local state
        this.items = this.items.filter(item => item.id !== itemId);
        
        // Re-render and update statistics
        this.filterAndRenderItems();
        await this.updateStatistics();
        
        this.showToast('Item deleted', 'success');
      } catch (error) {
        console.error('Failed to delete item:', error);
        this.showToast('Failed to delete item', 'error');
      }
    }
  }

  async deleteCategory(categoryId) {
    const category = this.categories.find(c => c.id === categoryId);
    if (!category) return;
    
    const itemCount = this.items.filter(item => item.category_id === categoryId).length;
    
    if (itemCount > 0) {
      this.showToast(`Cannot delete category with ${itemCount} items. Please reassign or delete items first.`, 'warning');
      return;
    }
    
    const result = await electronAPI.dialog.showMessage({
      type: 'warning',
      buttons: ['Cancel', 'Delete'],
      defaultId: 0,
      title: 'Delete Category',
      message: `Are you sure you want to delete the category "${category.name}"?`,
      detail: 'This action cannot be undone.'
    });
    
    if (result.response === 1) {
      try {
        await electronAPI.categories.delete(categoryId);
        
        // Remove from local state
        this.categories = this.categories.filter(cat => cat.id !== categoryId);
        
        // Re-render
        this.renderCategories();
        
        this.showToast('Category deleted', 'success');
      } catch (error) {
        console.error('Failed to delete category:', error);
        this.showToast('Failed to delete category', 'error');
      }
    }
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.app = new ShodaipatiApp();
});
