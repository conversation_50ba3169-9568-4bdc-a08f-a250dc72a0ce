const fs = require('fs').promises;
const path = require('path');
const ExcelJS = require('exceljs');
const { createObjectCsvWriter } = require('csv-writer');
const puppeteer = require('puppeteer');
const { htmlToImage } = require('html-to-image');

class ExportUtility {
  constructor() {
    this.supportedFormats = ['json', 'csv', 'excel', 'pdf', 'jpg'];
  }

  async exportToJSON(items, filePath, options = {}) {
    try {
      const exportData = this.prepareExportData(items, options);
      const jsonData = JSON.stringify(exportData, null, 2);
      
      await fs.writeFile(filePath, jsonData, 'utf8');
      return { success: true, filePath, itemCount: items.length };
    } catch (error) {
      throw new Error(`JSON export failed: ${error.message}`);
    }
  }

  async exportToCSV(items, filePath, options = {}) {
    try {
      if (items.length === 0) {
        throw new Error('No items to export');
      }

      // Prepare CSV headers
      const headers = [
        { id: 'name', title: 'Item Name' },
        { id: 'quantity', title: 'Quantity' },
        { id: 'unit', title: 'Unit' },
        { id: 'category_name', title: 'Category' },
        { id: 'purchased', title: 'Purchased' },
        { id: 'date_added', title: 'Date Added' }
      ];

      if (options.includeBrands) {
        headers.splice(4, 0, { id: 'brand', title: 'Brand' });
      }

      if (options.includePrices) {
        headers.splice(-2, 0, { id: 'price', title: 'Price' });
      }

      if (options.includeRemarks) {
        headers.push({ id: 'remarks', title: 'Remarks' });
      }

      // Create CSV writer
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: headers
      });

      // Prepare data
      const csvData = items.map(item => {
        const row = {
          name: item.name,
          quantity: item.quantity,
          unit: item.unit,
          category_name: item.category_name || 'Uncategorized',
          purchased: item.purchased ? 'Yes' : 'No',
          date_added: item.date_added
        };

        if (options.includeBrands) {
          row.brand = item.brand || '';
        }

        if (options.includePrices) {
          row.price = item.price || 0;
        }

        if (options.includeRemarks) {
          row.remarks = item.remarks || '';
        }

        return row;
      });

      await csvWriter.writeRecords(csvData);
      return { success: true, filePath, itemCount: items.length };
    } catch (error) {
      throw new Error(`CSV export failed: ${error.message}`);
    }
  }

  async exportToExcel(items, filePath, options = {}) {
    try {
      const workbook = new ExcelJS.Workbook();
      
      // Set workbook properties
      workbook.creator = 'Shodaipati';
      workbook.lastModifiedBy = 'Shodaipati';
      workbook.created = new Date();
      workbook.modified = new Date();

      if (options.groupByCategory) {
        await this.createExcelWithCategories(workbook, items, options);
      } else {
        await this.createExcelSingleSheet(workbook, items, options);
      }

      await workbook.xlsx.writeFile(filePath);
      return { success: true, filePath, itemCount: items.length };
    } catch (error) {
      throw new Error(`Excel export failed: ${error.message}`);
    }
  }

  async createExcelSingleSheet(workbook, items, options) {
    const worksheet = workbook.addWorksheet('Grocery List');

    // Define columns
    const columns = [
      { header: 'Item Name', key: 'name', width: 25 },
      { header: 'Quantity', key: 'quantity', width: 10 },
      { header: 'Unit', key: 'unit', width: 10 },
      { header: 'Category', key: 'category_name', width: 20 },
      { header: 'Purchased', key: 'purchased', width: 12 },
      { header: 'Date Added', key: 'date_added', width: 15 }
    ];

    if (options.includeBrands) {
      columns.splice(4, 0, { header: 'Brand', key: 'brand', width: 20 });
    }

    if (options.includePrices) {
      columns.splice(-2, 0, { header: 'Price', key: 'price', width: 12 });
    }

    if (options.includeRemarks) {
      columns.push({ header: 'Remarks', key: 'remarks', width: 30 });
    }

    worksheet.columns = columns;

    // Style header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    };

    // Add data
    items.forEach(item => {
      const row = {
        name: item.name,
        quantity: item.quantity,
        unit: item.unit,
        category_name: item.category_name || 'Uncategorized',
        purchased: item.purchased ? 'Yes' : 'No',
        date_added: item.date_added
      };

      if (options.includeBrands) {
        row.brand = item.brand || '';
      }

      if (options.includePrices) {
        row.price = item.price || 0;
      }

      if (options.includeRemarks) {
        row.remarks = item.remarks || '';
      }

      const addedRow = worksheet.addRow(row);
      
      // Style purchased items
      if (item.purchased) {
        addedRow.font = { strikethrough: true, color: { argb: 'FF888888' } };
      }
    });

    // Add borders
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });
  }

  async createExcelWithCategories(workbook, items, options) {
    // Group items by category
    const itemsByCategory = {};
    items.forEach(item => {
      const categoryName = item.category_name || 'Uncategorized';
      if (!itemsByCategory[categoryName]) {
        itemsByCategory[categoryName] = [];
      }
      itemsByCategory[categoryName].push(item);
    });

    // Create summary sheet
    const summarySheet = workbook.addWorksheet('Summary');
    summarySheet.columns = [
      { header: 'Category', key: 'category', width: 25 },
      { header: 'Total Items', key: 'total', width: 15 },
      { header: 'Purchased', key: 'purchased', width: 15 },
      { header: 'Remaining', key: 'remaining', width: 15 }
    ];

    if (options.includePrices) {
      summarySheet.columns.push(
        { header: 'Total Value', key: 'totalValue', width: 15 },
        { header: 'Purchased Value', key: 'purchasedValue', width: 18 }
      );
    }

    // Style summary header
    summarySheet.getRow(1).font = { bold: true };
    summarySheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    };

    // Add summary data
    Object.entries(itemsByCategory).forEach(([categoryName, categoryItems]) => {
      const purchased = categoryItems.filter(item => item.purchased).length;
      const total = categoryItems.length;
      const remaining = total - purchased;

      const summaryRow = {
        category: categoryName,
        total,
        purchased,
        remaining
      };

      if (options.includePrices) {
        const totalValue = categoryItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const purchasedValue = categoryItems
          .filter(item => item.purchased)
          .reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        summaryRow.totalValue = totalValue;
        summaryRow.purchasedValue = purchasedValue;
      }

      summarySheet.addRow(summaryRow);
    });

    // Create individual category sheets
    Object.entries(itemsByCategory).forEach(([categoryName, categoryItems]) => {
      const worksheet = workbook.addWorksheet(categoryName.substring(0, 31)); // Excel sheet name limit
      this.createExcelSingleSheet({ addWorksheet: () => worksheet }, categoryItems, options);
    });
  }

  prepareExportData(items, options) {
    const now = new Date();
    
    return {
      metadata: {
        exportDate: now.toISOString(),
        exportedBy: 'Shodaipati',
        version: '1.0.0',
        totalItems: items.length,
        options: {
          includePrices: options.includePrices || false,
          includeBrands: options.includeBrands || false,
          includeRemarks: options.includeRemarks || false,
          groupByCategory: options.groupByCategory || false,
          scope: options.scope || 'all'
        }
      },
      items: items.map(item => {
        const exportItem = {
          id: item.id,
          name: item.name,
          quantity: item.quantity,
          unit: item.unit,
          category_id: item.category_id,
          category_name: item.category_name,
          purchased: item.purchased,
          date_added: item.date_added,
          created_at: item.created_at,
          updated_at: item.updated_at
        };

        if (options.includeBrands) {
          exportItem.brand = item.brand;
        }

        if (options.includePrices) {
          exportItem.price = item.price;
        }

        if (options.includeRemarks) {
          exportItem.remarks = item.remarks;
        }

        return exportItem;
      })
    };
  }

  async importFromJSON(filePath) {
    try {
      const fileContent = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(fileContent);
      
      // Validate JSON structure
      if (!data.items || !Array.isArray(data.items)) {
        throw new Error('Invalid JSON format: missing items array');
      }

      return {
        success: true,
        items: data.items,
        metadata: data.metadata || {},
        itemCount: data.items.length
      };
    } catch (error) {
      throw new Error(`JSON import failed: ${error.message}`);
    }
  }

  async importFromCSV(filePath) {
    try {
      const csv = require('csv-parser');
      const items = [];
      
      return new Promise((resolve, reject) => {
        require('fs').createReadStream(filePath)
          .pipe(csv())
          .on('data', (row) => {
            // Convert CSV row to item format
            const item = {
              name: row['Item Name'] || row.name,
              quantity: parseFloat(row['Quantity'] || row.quantity) || 1,
              unit: row['Unit'] || row.unit || 'piece',
              brand: row['Brand'] || row.brand || '',
              category_name: row['Category'] || row.category_name || 'Uncategorized',
              price: parseFloat(row['Price'] || row.price) || 0,
              remarks: row['Remarks'] || row.remarks || '',
              purchased: (row['Purchased'] || row.purchased || '').toLowerCase() === 'yes',
              date_added: row['Date Added'] || row.date_added || new Date().toISOString().split('T')[0]
            };

            // Validate required fields
            if (item.name && item.name.trim()) {
              items.push(item);
            }
          })
          .on('end', () => {
            resolve({
              success: true,
              items,
              itemCount: items.length
            });
          })
          .on('error', (error) => {
            reject(new Error(`CSV import failed: ${error.message}`));
          });
      });
    } catch (error) {
      throw new Error(`CSV import failed: ${error.message}`);
    }
  }

  async exportToPDF(items, filePath, options = {}) {
    try {
      const html = this.generateHTML(items, options);

      const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: 'networkidle0' });

      await page.pdf({
        path: filePath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '15mm',
          bottom: '20mm',
          left: '15mm'
        }
      });

      await browser.close();

      return { success: true, filePath, itemCount: items.length };
    } catch (error) {
      throw new Error(`PDF export failed: ${error.message}`);
    }
  }

  async exportToJPG(items, filePath, options = {}) {
    try {
      const html = this.generateHTML(items, options);

      // Create a temporary HTML file
      const tempHtmlPath = path.join(path.dirname(filePath), 'temp_export.html');
      await fs.writeFile(tempHtmlPath, html);

      const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await browser.newPage();
      await page.goto(`file://${tempHtmlPath}`, { waitUntil: 'networkidle0' });

      // Set viewport for consistent image size
      await page.setViewport({ width: 800, height: 1200 });

      await page.screenshot({
        path: filePath,
        type: 'jpeg',
        quality: 90,
        fullPage: true
      });

      await browser.close();

      // Clean up temporary file
      try {
        await fs.unlink(tempHtmlPath);
      } catch (cleanupError) {
        console.warn('Failed to clean up temporary HTML file:', cleanupError);
      }

      return { success: true, filePath, itemCount: items.length };
    } catch (error) {
      throw new Error(`JPG export failed: ${error.message}`);
    }
  }

  generateHTML(items, options = {}) {
    const now = new Date();
    const monthYear = now.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

    // Group items by category if requested
    let content = '';
    if (options.groupByCategory) {
      const itemsByCategory = {};
      items.forEach(item => {
        const categoryName = item.category_name || 'Uncategorized';
        if (!itemsByCategory[categoryName]) {
          itemsByCategory[categoryName] = [];
        }
        itemsByCategory[categoryName].push(item);
      });

      content = Object.entries(itemsByCategory).map(([categoryName, categoryItems]) => {
        return this.generateCategorySection(categoryName, categoryItems, options);
      }).join('');
    } else {
      content = this.generateItemsList(items, options);
    }

    // Calculate statistics
    const totalItems = items.length;
    const purchasedItems = items.filter(item => item.purchased).length;
    const totalValue = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const purchasedValue = items.filter(item => item.purchased).reduce((sum, item) => sum + (item.price * item.quantity), 0);

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grocery List - ${monthYear}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #0ea5e9;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #0ea5e9;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #64748b;
            font-size: 1.2em;
            margin: 10px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #0ea5e9;
        }
        .stat-label {
            color: #64748b;
            font-size: 0.9em;
        }
        .category {
            margin-bottom: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .category-header {
            background: #0ea5e9;
            color: white;
            padding: 12px 20px;
            font-weight: bold;
            font-size: 1.1em;
        }
        .category-items {
            padding: 15px 20px;
        }
        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        .item:last-child {
            border-bottom: none;
        }
        .item.purchased {
            opacity: 0.6;
            text-decoration: line-through;
        }
        .item-info {
            flex: 1;
        }
        .item-name {
            font-weight: 500;
            color: #1e293b;
        }
        .item-details {
            font-size: 0.9em;
            color: #64748b;
            margin-top: 2px;
        }
        .item-status {
            margin-left: 15px;
        }
        .purchased-icon {
            color: #10b981;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #64748b;
            font-size: 0.9em;
            border-top: 1px solid #e2e8f0;
            padding-top: 20px;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .header h1 { font-size: 2em; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛒 Shodaipati</h1>
        <div class="subtitle">Monthly Grocery List - ${monthYear}</div>
        <div style="color: #64748b; font-size: 0.9em;">Generated on ${now.toLocaleDateString()}</div>
    </div>

    <div class="stats">
        <div class="stat-item">
            <div class="stat-value">${totalItems}</div>
            <div class="stat-label">Total Items</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">${purchasedItems}</div>
            <div class="stat-label">Purchased</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">${totalItems - purchasedItems}</div>
            <div class="stat-label">Remaining</div>
        </div>
        ${options.includePrices ? `
        <div class="stat-item">
            <div class="stat-value">৳${totalValue.toFixed(2)}</div>
            <div class="stat-label">Total Value</div>
        </div>
        ` : ''}
    </div>

    ${content}

    <div class="footer">
        <p>Generated by Shodaipati - Monthly Grocery List Manager</p>
        <p>Export Options: ${Object.entries(options).filter(([key, value]) => value).map(([key]) => key).join(', ') || 'Default'}</p>
    </div>
</body>
</html>`;
  }

  generateCategorySection(categoryName, items, options) {
    return `
    <div class="category">
        <div class="category-header">
            ${this.escapeHtml(categoryName)} (${items.length} items)
        </div>
        <div class="category-items">
            ${this.generateItemsList(items, options)}
        </div>
    </div>`;
  }

  generateItemsList(items, options) {
    return items.map(item => {
      let details = `${item.quantity} ${item.unit}`;

      if (options.includeBrands && item.brand) {
        details += ` • ${this.escapeHtml(item.brand)}`;
      }

      if (options.includePrices && item.price > 0) {
        details += ` • ৳${item.price.toFixed(2)}`;
      }

      if (options.includeRemarks && item.remarks) {
        details += ` • ${this.escapeHtml(item.remarks)}`;
      }

      return `
      <div class="item ${item.purchased ? 'purchased' : ''}">
          <div class="item-info">
              <div class="item-name">${this.escapeHtml(item.name)}</div>
              <div class="item-details">${details}</div>
          </div>
          <div class="item-status">
              ${item.purchased ? '<span class="purchased-icon">✓</span>' : '<span style="color: #cbd5e1;">○</span>'}
          </div>
      </div>`;
    }).join('');
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = { innerHTML: '' };
    div.textContent = text;
    return div.innerHTML;
  }

  validateImportData(items) {
    const errors = [];
    const validItems = [];

    items.forEach((item, index) => {
      const itemErrors = [];

      // Required fields
      if (!item.name || item.name.trim().length === 0) {
        itemErrors.push('Item name is required');
      }

      if (item.name && item.name.length > 100) {
        itemErrors.push('Item name must be 100 characters or less');
      }

      // Quantity validation
      if (item.quantity !== undefined && (isNaN(item.quantity) || item.quantity <= 0)) {
        itemErrors.push('Quantity must be a positive number');
      }

      // Price validation
      if (item.price !== undefined && (isNaN(item.price) || item.price < 0)) {
        itemErrors.push('Price must be a non-negative number');
      }

      // Unit validation
      const validUnits = ['Kg', 'g', 'L', 'mL', 'piece', 'dozen', 'pack'];
      if (item.unit && !validUnits.includes(item.unit)) {
        itemErrors.push(`Invalid unit. Must be one of: ${validUnits.join(', ')}`);
      }

      if (itemErrors.length > 0) {
        errors.push({
          row: index + 1,
          item: item.name || 'Unknown',
          errors: itemErrors
        });
      } else {
        validItems.push(item);
      }
    });

    return {
      valid: errors.length === 0,
      validItems,
      errors,
      totalItems: items.length,
      validCount: validItems.length,
      errorCount: errors.length
    };
  }
}

module.exports = new ExportUtility();
