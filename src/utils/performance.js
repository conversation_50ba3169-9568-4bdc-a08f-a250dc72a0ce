// Performance optimization utilities for Shodaipati
class PerformanceOptimizer {
  constructor() {
    this.cache = new Map();
    this.debounceTimers = new Map();
    this.throttleTimers = new Map();
    this.observers = new Map();
  }

  // Memoization for expensive operations
  memoize(fn, keyGenerator = (...args) => JSON.stringify(args)) {
    return (...args) => {
      const key = keyGenerator(...args);
      
      if (this.cache.has(key)) {
        return this.cache.get(key);
      }
      
      const result = fn(...args);
      this.cache.set(key, result);
      
      // Limit cache size to prevent memory leaks
      if (this.cache.size > 1000) {
        const firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
      }
      
      return result;
    };
  }

  // Clear cache for specific keys or all
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  // Enhanced debounce with immediate execution option
  debounce(fn, delay, immediate = false) {
    const key = fn.toString();
    
    return (...args) => {
      const callNow = immediate && !this.debounceTimers.has(key);
      
      if (this.debounceTimers.has(key)) {
        clearTimeout(this.debounceTimers.get(key));
      }
      
      this.debounceTimers.set(key, setTimeout(() => {
        this.debounceTimers.delete(key);
        if (!immediate) fn(...args);
      }, delay));
      
      if (callNow) fn(...args);
    };
  }

  // Enhanced throttle with leading and trailing options
  throttle(fn, limit, options = { leading: true, trailing: true }) {
    const key = fn.toString();
    let inThrottle = false;
    let lastArgs = null;
    
    return (...args) => {
      if (!inThrottle) {
        if (options.leading) {
          fn(...args);
        }
        inThrottle = true;
        
        setTimeout(() => {
          inThrottle = false;
          if (options.trailing && lastArgs) {
            fn(...lastArgs);
            lastArgs = null;
          }
        }, limit);
      } else {
        lastArgs = args;
      }
    };
  }

  // Virtual scrolling for large lists
  createVirtualScroller(container, itemHeight, renderItem, totalItems) {
    const containerHeight = container.clientHeight;
    const visibleCount = Math.ceil(containerHeight / itemHeight) + 2; // Buffer
    let scrollTop = 0;
    let startIndex = 0;
    
    const scrollHandler = this.throttle(() => {
      scrollTop = container.scrollTop;
      startIndex = Math.floor(scrollTop / itemHeight);
      render();
    }, 16); // ~60fps
    
    const render = () => {
      const endIndex = Math.min(startIndex + visibleCount, totalItems);
      const visibleItems = [];
      
      for (let i = startIndex; i < endIndex; i++) {
        visibleItems.push(renderItem(i));
      }
      
      container.innerHTML = `
        <div style="height: ${startIndex * itemHeight}px;"></div>
        ${visibleItems.join('')}
        <div style="height: ${(totalItems - endIndex) * itemHeight}px;"></div>
      `;
    };
    
    container.addEventListener('scroll', scrollHandler);
    render();
    
    return {
      update: (newTotalItems) => {
        totalItems = newTotalItems;
        render();
      },
      destroy: () => {
        container.removeEventListener('scroll', scrollHandler);
      }
    };
  }

  // Intersection Observer for lazy loading
  createLazyLoader(selector, callback, options = {}) {
    const defaultOptions = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1
    };
    
    const observerOptions = { ...defaultOptions, ...options };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          callback(entry.target);
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => observer.observe(el));
    
    this.observers.set(selector, observer);
    
    return observer;
  }

  // Batch DOM operations
  batchDOMUpdates(operations) {
    return new Promise(resolve => {
      requestAnimationFrame(() => {
        operations.forEach(op => op());
        resolve();
      });
    });
  }

  // Memory usage monitoring
  getMemoryUsage() {
    if (performance.memory) {
      return {
        used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) // MB
      };
    }
    return null;
  }

  // Performance timing
  createTimer(name) {
    const startTime = performance.now();
    
    return {
      end: () => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        console.log(`Timer ${name}: ${duration.toFixed(2)}ms`);
        return duration;
      }
    };
  }

  // Database query optimization
  optimizeQuery(query, params = []) {
    // Add query hints for better performance
    let optimizedQuery = query;
    
    // Add LIMIT if not present for SELECT queries
    if (query.trim().toUpperCase().startsWith('SELECT') && 
        !query.toUpperCase().includes('LIMIT')) {
      optimizedQuery += ' LIMIT 1000';
    }
    
    // Use prepared statements hint
    if (params.length > 0) {
      optimizedQuery = `-- PREPARED STATEMENT\n${optimizedQuery}`;
    }
    
    return optimizedQuery;
  }

  // Image optimization
  optimizeImage(imageElement, options = {}) {
    const defaultOptions = {
      quality: 0.8,
      maxWidth: 800,
      maxHeight: 600,
      format: 'webp'
    };
    
    const opts = { ...defaultOptions, ...options };
    
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      const img = new Image();
      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > opts.maxWidth) {
          height = (height * opts.maxWidth) / width;
          width = opts.maxWidth;
        }
        
        if (height > opts.maxHeight) {
          width = (width * opts.maxHeight) / height;
          height = opts.maxHeight;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(resolve, `image/${opts.format}`, opts.quality);
      };
      
      img.src = imageElement.src;
    });
  }

  // Resource preloading
  preloadResources(resources) {
    const promises = resources.map(resource => {
      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.url;
        link.as = resource.type || 'fetch';
        
        if (resource.type === 'font') {
          link.crossOrigin = 'anonymous';
        }
        
        link.onload = resolve;
        link.onerror = reject;
        
        document.head.appendChild(link);
      });
    });
    
    return Promise.allSettled(promises);
  }

  // Service Worker registration for caching
  registerServiceWorker(swPath = '/sw.js') {
    if ('serviceWorker' in navigator) {
      return navigator.serviceWorker.register(swPath)
        .then(registration => {
          console.log('Service Worker registered:', registration);
          return registration;
        })
        .catch(error => {
          console.error('Service Worker registration failed:', error);
          throw error;
        });
    }
    
    return Promise.reject(new Error('Service Workers not supported'));
  }

  // Cleanup method
  cleanup() {
    this.cache.clear();
    
    // Clear all timers
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
    
    this.throttleTimers.forEach(timer => clearTimeout(timer));
    this.throttleTimers.clear();
    
    // Disconnect observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }

  // Performance monitoring
  startPerformanceMonitoring() {
    const monitor = {
      fps: 0,
      memory: null,
      timing: {}
    };

    // FPS monitoring
    let frames = 0;
    let lastTime = performance.now();
    
    const countFPS = () => {
      frames++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        monitor.fps = Math.round((frames * 1000) / (currentTime - lastTime));
        frames = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(countFPS);
    };
    
    requestAnimationFrame(countFPS);

    // Memory monitoring
    setInterval(() => {
      monitor.memory = this.getMemoryUsage();
    }, 5000);

    // Navigation timing
    if (performance.timing) {
      const timing = performance.timing;
      monitor.timing = {
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
        loadComplete: timing.loadEventEnd - timing.navigationStart,
        domInteractive: timing.domInteractive - timing.navigationStart
      };
    }

    return monitor;
  }
}

// Create singleton instance
const performanceOptimizer = new PerformanceOptimizer();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = performanceOptimizer;
} else if (typeof window !== 'undefined') {
  window.performanceOptimizer = performanceOptimizer;
}
