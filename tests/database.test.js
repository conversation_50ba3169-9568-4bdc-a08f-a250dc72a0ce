const { expect } = require('chai');
const path = require('path');
const fs = require('fs');
const dbManager = require('../src/database/db');
const Category = require('../src/database/models/Category');
const Item = require('../src/database/models/Item');
const MonthlyList = require('../src/database/models/MonthlyList');
const Template = require('../src/database/models/Template');

describe('Database Tests', function() {
  let testDbPath;

  before(async function() {
    // Create a test database
    testDbPath = path.join(__dirname, 'test.db');
    
    // Override the database path for testing
    dbManager.dbPath = testDbPath;
    
    // Initialize test database
    await dbManager.initialize();
  });

  after(function() {
    // Clean up test database
    dbManager.close();
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
    }
  });

  beforeEach(function() {
    // Clear all data before each test
    const db = dbManager.getDatabase();
    db.exec('DELETE FROM template_items');
    db.exec('DELETE FROM list_items');
    db.exec('DELETE FROM items');
    db.exec('DELETE FROM templates');
    db.exec('DELETE FROM monthly_lists');
    db.exec('DELETE FROM categories');
  });

  describe('Category Model', function() {
    it('should create a new category', function() {
      const category = Category.create({ name: 'Test Category' });
      
      expect(category).to.have.property('id');
      expect(category.name).to.equal('Test Category');
      expect(category.sort_order).to.be.a('number');
    });

    it('should not allow duplicate category names', function() {
      Category.create({ name: 'Duplicate Category' });
      
      expect(() => {
        Category.create({ name: 'Duplicate Category' });
      }).to.throw('Category with this name already exists');
    });

    it('should validate category name length', function() {
      expect(() => {
        Category.create({ name: 'A'.repeat(31) });
      }).to.throw('Category name must be 30 characters or less');
    });

    it('should get all categories ordered by sort_order', function() {
      Category.create({ name: 'Category B', sort_order: 2 });
      Category.create({ name: 'Category A', sort_order: 1 });
      
      const categories = Category.getAll();
      expect(categories).to.have.length(2);
      expect(categories[0].name).to.equal('Category A');
      expect(categories[1].name).to.equal('Category B');
    });

    it('should update category', function() {
      const category = Category.create({ name: 'Original Name' });
      const updated = Category.update(category.id, { name: 'Updated Name' });
      
      expect(updated.name).to.equal('Updated Name');
    });

    it('should delete category without items', function() {
      const category = Category.create({ name: 'To Delete' });
      const result = Category.delete(category.id);
      
      expect(result).to.be.true;
      expect(Category.getById(category.id)).to.be.null;
    });

    it('should not delete category with items', function() {
      const category = Category.create({ name: 'With Items' });
      Item.create({ name: 'Test Item', category_id: category.id });
      
      expect(() => {
        Category.delete(category.id);
      }).to.throw('Cannot delete category that contains items');
    });
  });

  describe('Item Model', function() {
    let testCategory;

    beforeEach(function() {
      testCategory = Category.create({ name: 'Test Category' });
    });

    it('should create a new item', function() {
      const item = Item.create({
        name: 'Test Item',
        quantity: 2,
        unit: 'Kg',
        category_id: testCategory.id
      });
      
      expect(item).to.have.property('id');
      expect(item.name).to.equal('Test Item');
      expect(item.quantity).to.equal(2);
      expect(item.unit).to.equal('Kg');
      expect(item.category_id).to.equal(testCategory.id);
    });

    it('should validate required fields', function() {
      expect(() => {
        Item.create({ category_id: testCategory.id });
      }).to.throw('Item name is required');

      expect(() => {
        Item.create({ name: 'Test Item' });
      }).to.throw('Category is required');
    });

    it('should validate item name length', function() {
      expect(() => {
        Item.create({
          name: 'A'.repeat(101),
          category_id: testCategory.id
        });
      }).to.throw('Item name must be 100 characters or less');
    });

    it('should validate quantity', function() {
      expect(() => {
        Item.create({
          name: 'Test Item',
          quantity: -1,
          category_id: testCategory.id
        });
      }).to.throw('Quantity must be a positive number');
    });

    it('should validate price', function() {
      expect(() => {
        Item.create({
          name: 'Test Item',
          price: -10,
          category_id: testCategory.id
        });
      }).to.throw('Price must be a non-negative number');
    });

    it('should validate unit', function() {
      expect(() => {
        Item.create({
          name: 'Test Item',
          unit: 'invalid',
          category_id: testCategory.id
        });
      }).to.throw('Invalid unit');
    });

    it('should update item', function() {
      const item = Item.create({
        name: 'Original Item',
        category_id: testCategory.id
      });
      
      const updated = Item.update(item.id, { name: 'Updated Item', quantity: 5 });
      
      expect(updated.name).to.equal('Updated Item');
      expect(updated.quantity).to.equal(5);
    });

    it('should mark item as purchased', function() {
      const item = Item.create({
        name: 'Test Item',
        category_id: testCategory.id
      });
      
      const updated = Item.markPurchased(item.id, true);
      expect(updated.purchased).to.be.true;
    });

    it('should get items by category', function() {
      const category2 = Category.create({ name: 'Category 2' });
      
      Item.create({ name: 'Item 1', category_id: testCategory.id });
      Item.create({ name: 'Item 2', category_id: testCategory.id });
      Item.create({ name: 'Item 3', category_id: category2.id });
      
      const items = Item.getByCategory(testCategory.id);
      expect(items).to.have.length(2);
    });

    it('should get statistics', function() {
      Item.create({ name: 'Item 1', category_id: testCategory.id, price: 10, quantity: 2 });
      Item.create({ name: 'Item 2', category_id: testCategory.id, price: 5, quantity: 1, purchased: true });
      
      const stats = Item.getStatistics();
      expect(stats.total).to.equal(2);
      expect(stats.purchased).to.equal(1);
      expect(stats.totalValue).to.equal(25);
      expect(stats.purchasedValue).to.equal(5);
    });
  });

  describe('MonthlyList Model', function() {
    it('should create a monthly list', function() {
      const monthlyList = MonthlyList.create({
        month: 1,
        year: 2024,
        budget: 1000
      });
      
      expect(monthlyList.month).to.equal(1);
      expect(monthlyList.year).to.equal(2024);
      expect(monthlyList.budget).to.equal(1000);
    });

    it('should not allow duplicate month/year', function() {
      MonthlyList.create({ month: 1, year: 2024 });
      
      expect(() => {
        MonthlyList.create({ month: 1, year: 2024 });
      }).to.throw('Monthly list for this month already exists');
    });

    it('should validate month range', function() {
      expect(() => {
        MonthlyList.create({ month: 13, year: 2024 });
      }).to.throw('Month must be between 1 and 12');
    });

    it('should add items to monthly list', function() {
      const category = Category.create({ name: 'Test Category' });
      const item = Item.create({ name: 'Test Item', category_id: category.id });
      
      const result = MonthlyList.addItemToMonth(item.id, 1, 2024);
      expect(result).to.be.true;
      
      const items = MonthlyList.getItemsForMonth(1, 2024);
      expect(items).to.have.length(1);
      expect(items[0].id).to.equal(item.id);
    });

    it('should get statistics for month', function() {
      const category = Category.create({ name: 'Test Category' });
      const item1 = Item.create({ name: 'Item 1', category_id: category.id, price: 10 });
      const item2 = Item.create({ name: 'Item 2', category_id: category.id, price: 5, purchased: true });
      
      MonthlyList.addItemToMonth(item1.id, 1, 2024);
      MonthlyList.addItemToMonth(item2.id, 1, 2024);
      
      const stats = MonthlyList.getStatisticsForMonth(1, 2024);
      expect(stats.totalItems).to.equal(2);
      expect(stats.purchasedItems).to.equal(1);
      expect(stats.totalValue).to.equal(15);
      expect(stats.purchasedValue).to.equal(5);
    });
  });

  describe('Template Model', function() {
    it('should create a template', function() {
      const template = Template.create({
        name: 'Test Template',
        description: 'A test template'
      });
      
      expect(template.name).to.equal('Test Template');
      expect(template.description).to.equal('A test template');
    });

    it('should not allow duplicate template names', function() {
      Template.create({ name: 'Duplicate Template' });
      
      expect(() => {
        Template.create({ name: 'Duplicate Template' });
      }).to.throw('Template with this name already exists');
    });

    it('should create template from current items', function() {
      const category = Category.create({ name: 'Test Category' });
      const item1 = Item.create({ name: 'Item 1', category_id: category.id });
      const item2 = Item.create({ name: 'Item 2', category_id: category.id });
      
      const template = Template.createFromCurrentItems(
        'From Items Template',
        'Created from items',
        [item1.id, item2.id]
      );
      
      expect(template.name).to.equal('From Items Template');
      
      const templateItems = Template.getItemsForTemplate(template.id);
      expect(templateItems).to.have.length(2);
    });

    it('should apply template to current month', function() {
      const category = Category.create({ name: 'Test Category' });
      const item = Item.create({ name: 'Template Item', category_id: category.id });
      
      const template = Template.createFromCurrentItems(
        'Apply Template',
        'Test template',
        [item.id]
      );
      
      const now = new Date();
      const result = Template.applyTemplateToCurrentMonth(template.id);
      
      expect(result.addedItems).to.equal(1);
      
      const monthItems = MonthlyList.getItemsForMonth(now.getMonth() + 1, now.getFullYear());
      expect(monthItems).to.have.length(1);
    });
  });
});
