const { expect } = require('chai');
const path = require('path');
const fs = require('fs').promises;
const exportUtility = require('../src/utils/export');

describe('Export/Import Tests', function() {
  const testDataDir = path.join(__dirname, 'test-data');
  const sampleItems = [
    {
      id: 1,
      name: 'Test Item 1',
      quantity: 2,
      unit: 'Kg',
      brand: 'Test Brand',
      category_id: 1,
      category_name: 'Test Category',
      price: 10.50,
      remarks: 'Test remarks',
      purchased: false,
      date_added: '2024-01-01'
    },
    {
      id: 2,
      name: 'Test Item 2',
      quantity: 1,
      unit: 'piece',
      brand: '',
      category_id: 1,
      category_name: 'Test Category',
      price: 5.00,
      remarks: '',
      purchased: true,
      date_added: '2024-01-02'
    }
  ];

  before(async function() {
    // Create test data directory
    try {
      await fs.mkdir(testDataDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
  });

  after(async function() {
    // Clean up test files
    try {
      const files = await fs.readdir(testDataDir);
      for (const file of files) {
        await fs.unlink(path.join(testDataDir, file));
      }
      await fs.rmdir(testDataDir);
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('JSON Export/Import', function() {
    it('should export items to JSON', async function() {
      const filePath = path.join(testDataDir, 'test-export.json');
      const options = {
        includePrices: true,
        includeBrands: true,
        includeRemarks: true
      };

      const result = await exportUtility.exportToJSON(sampleItems, filePath, options);
      
      expect(result.success).to.be.true;
      expect(result.itemCount).to.equal(2);
      
      // Verify file exists and has correct content
      const fileContent = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(fileContent);
      
      expect(data.metadata).to.exist;
      expect(data.items).to.have.length(2);
      expect(data.items[0].name).to.equal('Test Item 1');
    });

    it('should import items from JSON', async function() {
      const filePath = path.join(testDataDir, 'test-import.json');
      
      // Create test JSON file
      const testData = {
        metadata: {
          exportDate: new Date().toISOString(),
          totalItems: 1
        },
        items: [
          {
            name: 'Imported Item',
            quantity: 3,
            unit: 'L',
            category_name: 'Imported Category',
            price: 15.00,
            purchased: false
          }
        ]
      };
      
      await fs.writeFile(filePath, JSON.stringify(testData, null, 2));
      
      const result = await exportUtility.importFromJSON(filePath);
      
      expect(result.success).to.be.true;
      expect(result.itemCount).to.equal(1);
      expect(result.items[0].name).to.equal('Imported Item');
    });
  });

  describe('CSV Export/Import', function() {
    it('should export items to CSV', async function() {
      const filePath = path.join(testDataDir, 'test-export.csv');
      const options = {
        includePrices: true,
        includeBrands: true,
        includeRemarks: true
      };

      const result = await exportUtility.exportToCSV(sampleItems, filePath, options);
      
      expect(result.success).to.be.true;
      expect(result.itemCount).to.equal(2);
      
      // Verify file exists
      const fileContent = await fs.readFile(filePath, 'utf8');
      expect(fileContent).to.include('Item Name');
      expect(fileContent).to.include('Test Item 1');
    });

    it('should import items from CSV', async function() {
      const filePath = path.join(testDataDir, 'test-import.csv');
      
      // Create test CSV file
      const csvContent = `Item Name,Quantity,Unit,Category,Brand,Price,Purchased,Date Added
Imported CSV Item,2,Kg,CSV Category,CSV Brand,20.00,No,2024-01-01`;
      
      await fs.writeFile(filePath, csvContent);
      
      const result = await exportUtility.importFromCSV(filePath);
      
      expect(result.success).to.be.true;
      expect(result.itemCount).to.equal(1);
      expect(result.items[0].name).to.equal('Imported CSV Item');
    });
  });

  describe('Excel Export', function() {
    it('should export items to Excel', async function() {
      const filePath = path.join(testDataDir, 'test-export.xlsx');
      const options = {
        includePrices: true,
        includeBrands: true,
        groupByCategory: false
      };

      const result = await exportUtility.exportToExcel(sampleItems, filePath, options);
      
      expect(result.success).to.be.true;
      expect(result.itemCount).to.equal(2);
      
      // Verify file exists
      const stats = await fs.stat(filePath);
      expect(stats.size).to.be.greaterThan(0);
    });

    it('should export items to Excel grouped by category', async function() {
      const filePath = path.join(testDataDir, 'test-export-grouped.xlsx');
      const options = {
        includePrices: true,
        groupByCategory: true
      };

      const result = await exportUtility.exportToExcel(sampleItems, filePath, options);
      
      expect(result.success).to.be.true;
      expect(result.itemCount).to.equal(2);
    });
  });

  describe('Data Validation', function() {
    it('should validate import data correctly', function() {
      const validItems = [
        {
          name: 'Valid Item',
          quantity: 1,
          unit: 'piece',
          price: 10
        }
      ];

      const invalidItems = [
        {
          name: '', // Invalid: empty name
          quantity: 1
        },
        {
          name: 'A'.repeat(101), // Invalid: name too long
          quantity: 1
        },
        {
          name: 'Valid Name',
          quantity: -1 // Invalid: negative quantity
        },
        {
          name: 'Valid Name',
          quantity: 1,
          price: -5 // Invalid: negative price
        },
        {
          name: 'Valid Name',
          quantity: 1,
          unit: 'invalid' // Invalid: invalid unit
        }
      ];

      const validResult = exportUtility.validateImportData(validItems);
      expect(validResult.valid).to.be.true;
      expect(validResult.validCount).to.equal(1);
      expect(validResult.errorCount).to.equal(0);

      const invalidResult = exportUtility.validateImportData(invalidItems);
      expect(invalidResult.valid).to.be.false;
      expect(invalidResult.validCount).to.equal(0);
      expect(invalidResult.errorCount).to.equal(5);
    });

    it('should handle mixed valid and invalid data', function() {
      const mixedItems = [
        {
          name: 'Valid Item',
          quantity: 1,
          unit: 'piece'
        },
        {
          name: '', // Invalid
          quantity: 1
        },
        {
          name: 'Another Valid Item',
          quantity: 2,
          unit: 'Kg'
        }
      ];

      const result = exportUtility.validateImportData(mixedItems);
      expect(result.valid).to.be.false;
      expect(result.validCount).to.equal(2);
      expect(result.errorCount).to.equal(1);
    });
  });

  describe('Export Data Preparation', function() {
    it('should prepare export data with all options', function() {
      const options = {
        includePrices: true,
        includeBrands: true,
        includeRemarks: true,
        groupByCategory: true,
        scope: 'all'
      };

      const exportData = exportUtility.prepareExportData(sampleItems, options);
      
      expect(exportData.metadata).to.exist;
      expect(exportData.metadata.totalItems).to.equal(2);
      expect(exportData.metadata.options).to.deep.equal(options);
      expect(exportData.items).to.have.length(2);
      expect(exportData.items[0]).to.have.property('brand');
      expect(exportData.items[0]).to.have.property('price');
      expect(exportData.items[0]).to.have.property('remarks');
    });

    it('should prepare export data with limited options', function() {
      const options = {
        includePrices: false,
        includeBrands: false,
        includeRemarks: false
      };

      const exportData = exportUtility.prepareExportData(sampleItems, options);
      
      expect(exportData.items[0]).to.not.have.property('brand');
      expect(exportData.items[0]).to.not.have.property('price');
      expect(exportData.items[0]).to.not.have.property('remarks');
    });
  });

  describe('Error Handling', function() {
    it('should handle invalid file paths', async function() {
      const invalidPath = '/invalid/path/file.json';
      
      try {
        await exportUtility.exportToJSON(sampleItems, invalidPath, {});
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('JSON export failed');
      }
    });

    it('should handle empty items array', async function() {
      const filePath = path.join(testDataDir, 'empty-export.csv');
      
      try {
        await exportUtility.exportToCSV([], filePath, {});
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('No items to export');
      }
    });

    it('should handle invalid JSON import', async function() {
      const filePath = path.join(testDataDir, 'invalid.json');
      await fs.writeFile(filePath, 'invalid json content');
      
      try {
        await exportUtility.importFromJSON(filePath);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('JSON import failed');
      }
    });
  });
});
